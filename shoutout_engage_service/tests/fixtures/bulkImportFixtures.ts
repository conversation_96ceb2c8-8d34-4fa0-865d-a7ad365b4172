/**
 * Test fixtures for bulk import-related tests
 */
export const bulkImportFixtures = {
  validFieldMappings: [
    {
      fileColumnName: 'Name',
      systemAttributeName: 'name'
    },
    {
      fileColumnName: 'Email',
      systemAttributeName: 'email'
    },
    {
      fileColumnName: 'Phone',
      systemAttributeName: 'phone'
    },
    {
      fileColumnName: 'Country',
      systemAttributeName: 'country'
    }
  ],

  validBulkImportRequest: {
    fieldMappings: [
      {
        fileColumnName: 'Name',
        systemAttributeName: 'name'
      },
      {
        fileColumnName: 'Email',
        systemAttributeName: 'email'
      },
      {
        fileColumnName: 'Phone',
        systemAttributeName: 'phone'
      },
      {
        fileColumnName: 'Country',
        systemAttributeName: 'country'
      }
    ]
  },

  minimalValidBulkImportRequest: {
    fieldMappings: [
      {
        fileColumnName: 'Name',
        systemAttributeName: 'name'
      },
      {
        fileColumnName: 'Email',
        systemAttributeName: 'email'
      },
      {
        fileColumnName: 'Phone',
        systemAttributeName: 'phone'
      }
    ]
  },

  invalidBulkImportRequests: {
    missingFieldMappings: {
      // fieldMappings is missing
    },
    emptyFieldMappings: {
      fieldMappings: []
    },
    missingRequiredField: {
      fieldMappings: [
        {
          fileColumnName: 'Name',
          systemAttributeName: 'name'
        },
        {
          fileColumnName: 'Email',
          systemAttributeName: 'email'
        }
        // Missing phone mapping
      ]
    },
    missingFileColumnName: {
      fieldMappings: [
        {
          // fileColumnName is missing
          systemAttributeName: 'name'
        },
        {
          fileColumnName: 'Email',
          systemAttributeName: 'email'
        },
        {
          fileColumnName: 'Phone',
          systemAttributeName: 'phone'
        }
      ]
    },
    missingSystemAttributeName: {
      fieldMappings: [
        {
          fileColumnName: 'Name',
          // systemAttributeName is missing
        },
        {
          fileColumnName: 'Email',
          systemAttributeName: 'email'
        },
        {
          fileColumnName: 'Phone',
          systemAttributeName: 'phone'
        }
      ]
    }
  },

  sampleCSVData: [
    {
      Name: 'John Doe',
      Email: '<EMAIL>',
      Phone: '+1234567890',
      Country: 'United States'
    },
    {
      Name: 'Jane Smith',
      Email: '<EMAIL>',
      Phone: '+0987654321',
      Country: 'Canada'
    }
  ]
};
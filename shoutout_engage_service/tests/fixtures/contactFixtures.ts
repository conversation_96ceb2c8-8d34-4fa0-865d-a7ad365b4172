import { ObjectId } from 'mongodb';
import {  ContactStatus } from '../../types/contact.types';

/**
 * Test fixtures for contact-related tests
 */
export const contactFixtures = {
  validContactRequest: {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    country: 'United States',
    country_code: 'US',
    tags: [
      {
        tag_id: new ObjectId().toString(),
        tag_name: 'customer'
      }
    ],
    additional_fields: {
      company: 'Acme Corp',
      position: 'Manager',
      priority: 'high'
    },
    avatar_url: "https://example.com/avatar.jpg"
  },

  minimalValidContactRequest: {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+0987654321'
  },

  invalidContactRequests: {
    missingName: {
      email: '<EMAIL>',
      phone: '+1234567890'
    },
    missingEmail: {
      name: 'Test User',
      phone: '+1234567890'
    },
    missingPhone: {
      name: 'Test User',
      email: '<EMAIL>'
    },
    invalidEmail: {
      name: 'Test User',
      email: 'invalid-email',
      phone: '+1234567890'
    },
    emptyName: {
      name: '',
      email: '<EMAIL>',
      phone: '+1234567890'
    },
    invalidCountryCode: {
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+1234567890',
      country_code: 'INVALID'
    },
    invalidAvatarUrl: {
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+1234567890',
      avatar_url: 'not-a-valid-url'
    }
  },

  sampleContacts: [
    {
      _id: new ObjectId(),
      org_id: new ObjectId().toString(),
      created_by: new ObjectId().toString(),
      name: 'Alice Johnson',
      email: '<EMAIL>',
      phone: '+1111111111',
      country: 'Canada',
      country_code: 'CA',
      tags: [
        { tag_id: new ObjectId().toString(), tag_name: 'vip' },
        { tag_id: new ObjectId().toString(), tag_name: 'customer' }
      ],
      additional_fields: {
        company: 'Tech Corp',
        department: 'Engineering'
      },
      status: 'active' as ContactStatus,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      _id: new ObjectId(),
      org_id: new ObjectId().toString(),
      created_by: new ObjectId().toString(),
      name: 'Bob Wilson',
      email: '<EMAIL>',
      phone: '+2222222222',
      tags: [],
      additional_fields: {},
      status: 'active' as ContactStatus,
      created_at: new Date(),
      updated_at: new Date()
    }
  ],

  duplicateContactScenario: {
    existing: {
      _id: new ObjectId(),
      org_id: 'org123',
      created_by: 'user123',
      name: 'Existing User',
      email: '<EMAIL>',
      phone: '+5555555555',
      tags: [],
      additional_fields: {},
      status: 'active' as ContactStatus,
      created_at: new Date(),
      updated_at: new Date()
    },
    duplicate: {
      name: 'Another User',
      email: '<EMAIL>', // Same email
      phone: '+6666666666'
    }
  }
};

/**
 * Organization and user fixtures for testing
 */
export const organizationFixtures = {
  sampleOrganization: {
    id: new ObjectId().toString(),
    name: 'Test Organization',
    created_at: new Date()
  },

  sampleUser: {
    id: new ObjectId().toString(),
    org_id: new ObjectId().toString(),
    email: '<EMAIL>',
    name: 'Test User'
  }
};

import { ObjectId } from 'mongodb';
import { Contact, ContactStatus, ContactTag } from '../../types/contact.types';
import { Application } from 'express';
import http from 'http';
import request from 'supertest';
import createApp from '../../app';

/**
 * Test data factory for creating mock contacts
 */
export class TestDataFactory {
  static createValidContactData(overrides: Partial<Contact> = {}): Partial<Contact> {
    return {
      org_id: new ObjectId().toString(),
      created_by: new ObjectId().toString(),
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567890',
      country: 'United States',
      country_code: 'US',
      tags: [],
      additional_fields: {},
      status: ContactStatus.ACTIVE,
      ...overrides
    };
  }

  static createValidContactTag(overrides: Partial<ContactTag> = {}): ContactTag {
    return {
      tag_id: new ObjectId(),
      tag_name: 'test-tag',
      ...overrides
    };
  }

  static createInvalidContactData(): Partial<Contact> {
    return {
      // Missing required fields intentionally
      org_id: new ObjectId().toString(),
      additional_fields: {}
    };
  }
}

/**
 * Mock response helpers for testing HTTP responses
 */
export class MockResponseHelper {
  static createMockResponse() {
    const res: any = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    return res;
  }

  static createMockRequest(body: any = {}, params: any = {}, query: any = {}) {
    const userId = new ObjectId().toString();
    const orgId = new ObjectId().toString();
    
    // Create the request object with user property
    const req: any = {
      body,
      params,
      query,
      userId,
      user: {
        id: userId,
        org_id: orgId
      }
    };
    
    // Define a getter for organizationId that derives it from user.org_id
    Object.defineProperty(req, 'organizationId', {
      get: function() {
        // Return the org_id from the user object
        return this.user?.org_id;
      },
      // Allow tests to override the organizationId if needed
      set: function(value) {
        // If organizationId is explicitly set, define it as a regular property
        Object.defineProperty(this, 'organizationId', {
          value: value,
          writable: true,
          configurable: true
        });
      },
      configurable: true
    });
    
    return req;
  }
}

/**
 * Database test utilities
 */
export class DatabaseTestUtils {
  static async clearCollection(collectionName: string) {
    const mongoose = await import('mongoose');
    if (mongoose.connection && mongoose.connection.collections) {
      const collection = mongoose.connection.collections[collectionName];
      if (collection) {
        await collection.deleteMany({});
      }
    }
  }

  static async getCollectionCount(collectionName: string): Promise<number> {
    const mongoose = await import('mongoose');
    if (mongoose.connection && mongoose.connection.collections) {
      const collection = mongoose.connection.collections[collectionName];
      return collection ? await collection.countDocuments({}) : 0;
    }
    return 0;
  }
}

/**
 * Async test utilities
 */
export class AsyncTestUtils {
  static async expectAsync(asyncFn: () => Promise<any>): Promise<any> {
    try {
      return await asyncFn();
    } catch (error) {
      throw error;
    }
  }

  static async expectAsyncToThrow(asyncFn: () => Promise<any>, expectedError?: string | RegExp): Promise<void> {
    try {
      await asyncFn();
      throw new Error('Expected function to throw, but it did not');
    } catch (error) {
      if (expectedError) {
        if (typeof expectedError === 'string') {
          expect((error as Error).message).toContain(expectedError);
        } else {
          expect((error as Error).message).toMatch(expectedError);
        }
      }
    }
  }
}

/**
 * Server test utilities for integration tests
 * Provides a single entry point for connecting to the app server
 */
export class ServerTestUtils {
  private static instance: ServerTestUtils | null = null;
  private app: Application | null = null;
  private server: http.Server | null = null;

  private constructor() {}

  /**
   * Get the singleton instance of ServerTestUtils
   */
  static getInstance(): ServerTestUtils {
    if (!ServerTestUtils.instance) {
      ServerTestUtils.instance = new ServerTestUtils();
    }
    return ServerTestUtils.instance;
  }

  /**
   * Initialize the app server for testing
   * @param skipDbInit Whether to skip database initialization
   * @returns The initialized app instance
   */
  async initializeApp(skipDbInit: boolean = true): Promise<Application> {
    if (!this.app) {
      const { app, server } = await createApp(skipDbInit);
      this.app = app;
      this.server = server;
    }
    return this.app;
  }

  /**
   * Get a supertest instance for making requests to the app
   * @returns A supertest instance connected to the app
   */
  async getRequestAgent() {
    const app = await this.initializeApp();
    return request(app);
  }

  /**
   * Close the server and clean up resources
   */
  async closeServer(): Promise<void> {
    if (this.server) {
      await new Promise<void>((resolve, reject) => {
        this.server!.close((err) => {
          if (err) reject(err);
          else resolve();
        });
      });
      this.server = null;
      this.app = null;
    }
  }
}

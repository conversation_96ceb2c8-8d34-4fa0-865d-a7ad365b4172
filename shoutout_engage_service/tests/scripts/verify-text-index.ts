/**
 * <PERSON><PERSON><PERSON> to verify that the text index is properly created and working
 * Run with: npx ts-node tests/scripts/verify-text-index.ts
 */

import mongoose from 'mongoose';
import { ContactModel } from '../../lib/db/models/contact.model';
import { logger } from '../../lib/logger';
import config from '../../lib/config';

const log = logger(config.logger);

async function verifyTextIndex() {
  try {
    // Connect to MongoDB
    const mongoUrl = process.env.MONGO_URL || 'mongodb://localhost:27017/engage';
    log.info(`Connecting to MongoDB: ${mongoUrl.replace(/\/\/[^:]+:[^@]+@/, '//***:***@')}`);
    
    await mongoose.connect(mongoUrl);
    log.info('Connected to MongoDB');

    // List all indexes on the contacts collection to verify text index exists
    log.info('Listing indexes on contacts collection:');
    const indexes = await ContactModel.collection.indexes();
    log.info(JSON.stringify(indexes, null, 2));

    // Check if text index exists
    const textIndex = indexes.find(index => index.name === 'contact_text_search');
    if (textIndex) {
      log.info('✅ Text index found:', textIndex);
    } else {
      log.error('❌ Text index not found!');
    }

    // Try a simple $text search query
    log.info('Testing $text search query...');
    const searchTerm = 'test';
    const query = {
      $or: [
        { $text: { $search: searchTerm } },
        { email: new RegExp(searchTerm, 'i') },
        { phone: new RegExp(searchTerm, 'i') }
      ]
    };

    const results = await ContactModel.find(query).limit(5);
    log.info(`Search query executed successfully. Found ${results.length} results.`);

    log.info('✅ Text index is working properly!');
  } catch (error) {
    log.error('Error verifying text index:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    log.info('MongoDB connection closed');
  }
}

// Run the verification
verifyTextIndex();
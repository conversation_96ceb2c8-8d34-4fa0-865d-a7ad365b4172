import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { closeLoggers } from '../lib/logger';

let mongoServer: MongoMemoryServer;

// Setup MongoDB Memory Server before all tests
beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();

  await mongoose.connect(mongoUri);

  // Ensure indexes are created for all models
  // This is important for testing unique constraints
  const { ContactModel } = await import('../lib/db/models/contact.model');
  await ContactModel.createIndexes();
});

// Clean up after each test
afterEach(async () => {
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// Cleanup after all tests
afterAll(async () => {
  // Close database connections
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  await mongoServer.stop();
  
  // Close all logger instances to prevent open handles
  await closeLoggers();
}, 30000); // Increase timeout for cleanup operations

// Increase timeout for database operations
jest.setTimeout(30000);
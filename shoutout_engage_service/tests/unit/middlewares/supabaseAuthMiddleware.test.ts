import { Response, NextFunction } from 'express';
import { supabaseAuthMiddleware, AuthenticatedRequest, getSupabaseClient } from '../../../lib/middlewares/supabase.authorizer.middleware';
import { ProfileDAO } from '../../../lib/db/dao/prisma/ProfileDAO';
import { organizationFixtures } from '../../fixtures/contactFixtures';
import { UserProfile } from '../../../types/user.profile.types';

/**
 * Helper function to execute the middleware with standard parameters
 */
const executeMiddleware = async (
  req: Partial<AuthenticatedRequest>,
  res: Partial<Response>,
  next: NextFunction
): Promise<void> => {
  await supabaseAuthMiddleware(
    req as AuthenticatedRequest,
    res as Response,
    next
  );
};

// Mock getSupabaseClient
jest.mock('../../../lib/middlewares/supabase.authorizer.middleware', () => {
  const originalModule = jest.requireActual('../../../lib/middlewares/supabase.authorizer.middleware');
  return {
    ...originalModule,
    getSupabaseClient: jest.fn()
  };
});

// Mock ProfileDAO
jest.mock('../../../lib/db/dao/prisma/ProfileDAO');
const mockProfileDAO = ProfileDAO as jest.Mocked<typeof ProfileDAO>;

// Mock logger
jest.mock('../../../lib/logger', () => ({
  logger: () => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })
}));

// Mock config files
jest.mock('../../../config', () => ({
  SUPABASE_URL: 'https://test.supabase.co',
  SUPABASE_SERVICE_ROLE_KEY: 'test-service-role-key'
}));

jest.mock('../../../lib/config', () => ({
  logger: { level: 'info' }
}));

// Mock Supabase client
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn()
}));

const createClientMock = require('@supabase/supabase-js').createClient;
const mockSupabaseAuth = {
  getUser: jest.fn()
};

(createClientMock as jest.Mock).mockReturnValue({
  auth: mockSupabaseAuth
});

describe('supabaseAuthMiddleware', () => {
  let mockReq: Partial<AuthenticatedRequest>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockReq = {
      headers: {}
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    mockNext = jest.fn();

    // Reset all mocks
    jest.clearAllMocks();

    // Setup getSupabaseClient mock to return our mocked client
    (getSupabaseClient as jest.Mock).mockReturnValue({
      auth: mockSupabaseAuth
    });
  });

  describe('Successful authentication', () => {
    const mockUser = {
      id: organizationFixtures.sampleUser.id,
      email: '<EMAIL>',
      aud: 'authenticated',
      role: 'authenticated'
    };

    const mockProfile: UserProfile = {
      uuid: 'profile-uuid-123',
      user_id: organizationFixtures.sampleUser.id,
      organization_uuid: organizationFixtures.sampleOrganization.id,
      first_name: 'Test',
      last_name: 'User',
      user_type: 'user',
      email: '<EMAIL>',
      phone_number: '+**********',
      is_email_verified: true,
      is_mobile_verified: false,
      user_status: 'active',
      created_at: new Date('2024-01-01T00:00:00Z'),
      updated_at: new Date('2024-01-01T00:00:00Z')
    };

    it('should authenticate user with valid Bearer token and organization', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(mockProfile);

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockSupabaseAuth.getUser).toHaveBeenCalledWith('valid-token');
      expect(mockProfileDAO.getProfileByUserId).toHaveBeenCalledWith(mockUser.id);
      expect(mockReq.user).toEqual(mockUser);
      expect(mockReq.userId).toBe(mockUser.id);
      expect(mockReq.organizationId).toBe(mockProfile.organization_uuid);
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should authenticate user with valid token without Bearer prefix', async () => {
      mockReq.headers!.authorization = 'valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(mockProfile);

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockSupabaseAuth.getUser).toHaveBeenCalledWith('valid-token');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should authenticate user without organization context', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(null); // No profile found

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.user).toEqual(mockUser);
      expect(mockReq.userId).toBe(mockUser.id);
      expect(mockReq.organizationId).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
    });

    it('should authenticate user with profile but no organization', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue({
        ...mockProfile,
        organization_uuid: null
      });

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.user).toEqual(mockUser);
      expect(mockReq.userId).toBe(mockUser.id);
      expect(mockReq.organizationId).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('Authentication failures', () => {
    it('should return 401 when authorization header is missing', async () => {
      // No authorization header
      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Authorization header is required',
        errorCode: 'AUTH_001'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when token is empty', async () => {
      mockReq.headers!.authorization = 'Bearer ';

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Authentication token is required',
        errorCode: 'AUTH_002'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when token is invalid', async () => {
      mockReq.headers!.authorization = 'Bearer invalid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token' }
      });

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Invalid or expired authentication token',
        errorCode: 'AUTH_003'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when user is null', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      });

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Invalid or expired authentication token',
        errorCode: 'AUTH_003'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when Supabase returns error', async () => {
      mockReq.headers!.authorization = 'Bearer expired-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Token expired' }
      });

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Invalid or expired authentication token',
        errorCode: 'AUTH_003'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Error handling', () => {
    const mockUser = {
      id: organizationFixtures.sampleUser.id,
      email: '<EMAIL>',
      aud: 'authenticated',
      role: 'authenticated'
    };

    it('should return 500 when Supabase client throws error', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockRejectedValue(new Error('Network error'));

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Internal server error during authentication',
        errorCode: 'AUTH_004'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 500 when ProfileDAO throws error', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockRejectedValue(new Error('Database error'));

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Internal server error during authentication',
        errorCode: 'AUTH_004'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle ProfileDAO returning undefined gracefully', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(undefined);

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.user).toEqual(mockUser);
      expect(mockReq.userId).toBe(mockUser.id);
      expect(mockReq.organizationId).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('Organization context extraction', () => {
    const mockUser = {
      id: organizationFixtures.sampleUser.id,
      email: '<EMAIL>',
      aud: 'authenticated',
      role: 'authenticated'
    };

    const createMockProfile = (organizationUuid: string | null | undefined = 'org-123'): UserProfile => ({
      uuid: 'profile-uuid-456',
      user_id: mockUser.id,
      organization_uuid: organizationUuid,
      first_name: 'Test',
      last_name: 'User',
      user_type: 'user',
      email: mockUser.email,
      phone_number: '+**********',
      is_email_verified: true,
      is_mobile_verified: false,
      user_status: 'active',
      created_at: new Date('2024-01-01T00:00:00Z'),
      updated_at: new Date('2024-01-01T00:00:00Z')
    });

    it('should handle profile with organization_uuid', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(createMockProfile('org-123'));

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.organizationId).toBe('org-123');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle profile with null organization_uuid', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(createMockProfile(null));

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.organizationId).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle profile with empty string organization_uuid', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(createMockProfile(''));

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.organizationId).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('Token format handling', () => {
    const mockUser = {
      id: organizationFixtures.sampleUser.id,
      email: '<EMAIL>',
      aud: 'authenticated',
      role: 'authenticated'
    };

    it('should handle Bearer token format', async () => {
      mockReq.headers!.authorization = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(null);

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockSupabaseAuth.getUser).toHaveBeenCalledWith('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle token without Bearer prefix', async () => {
      mockReq.headers!.authorization = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(null);

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockSupabaseAuth.getUser).toHaveBeenCalledWith('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle malformed Bearer format', async () => {
      mockReq.headers!.authorization = 'Bearer';

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Authentication token is required',
        errorCode: 'AUTH_002'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Request object modification', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      aud: 'authenticated',
      role: 'authenticated',
      app_metadata: { provider: 'email' },
      user_metadata: { name: 'Test User' }
    };

    const mockProfileWithOrg: UserProfile = {
      uuid: 'profile-uuid-789',
      user_id: mockUser.id,
      organization_uuid: 'org-456',
      first_name: 'Test',
      last_name: 'User',
      user_type: 'user',
      email: mockUser.email,
      phone_number: '+**********',
      is_email_verified: true,
      is_mobile_verified: false,
      user_status: 'active',
      created_at: new Date('2024-01-01T00:00:00Z'),
      updated_at: new Date('2024-01-01T00:00:00Z')
    };

    it('should properly set all user properties on request object', async () => {
      mockReq.headers!.authorization = 'Bearer valid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });
      mockProfileDAO.getProfileByUserId.mockResolvedValue(mockProfileWithOrg);

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.user).toEqual(mockUser);
      expect(mockReq.userId).toBe(mockUser.id);
      expect(mockReq.organizationId).toBe('org-456');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should not modify request object on authentication failure', async () => {
      mockReq.headers!.authorization = 'Bearer invalid-token';
      mockSupabaseAuth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token' }
      });

      await executeMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.user).toBeUndefined();
      expect(mockReq.userId).toBeUndefined();
      expect(mockReq.organizationId).toBeUndefined();
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});

/**
 * Sample test to verify Jest setup is working correctly
 */

describe('Jest Setup Verification', () => {
  it('should run basic tests', () => {
    expect(true).toBe(true);
  });

  it('should support async tests', async () => {
    const result = await Promise.resolve('test');
    expect(result).toBe('test');
  });

  it('should support custom matchers', () => {
    const testEmail = '<EMAIL>';
    const testObjectId = '507f1f77bcf86cd799439011';
    const testPhone = '+1234567890';

    expect(testEmail).toBeValidEmail();
    expect(testObjectId).toBeValidObjectId();
    expect(testPhone).toBeValidPhoneNumber();
  });

  it('should have access to test utilities', () => {
    const { TestDataFactory } = require('../utils/testHelpers');
    const contactData = TestDataFactory.createValidContactData();
    
    expect(contactData).toHaveProperty('name');
    expect(contactData).toHaveProperty('email');
    expect(contactData).toHaveProperty('phone');
  });
});
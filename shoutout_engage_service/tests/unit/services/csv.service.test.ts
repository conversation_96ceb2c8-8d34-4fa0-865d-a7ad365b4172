import { CSVService } from '../../../services/csv.service';
import { ERROR_CODES } from '../../../lib/constant/file.constants';

// Mock Express.Multer.File type
interface MockFile {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
  size: number;
}

describe('CSVService', () => {
  describe('extractHeaders', () => {
    it('should extract headers from a CSV file', async () => {
      // Create a mock CSV file
      const csvContent = 'name,email,phone\nJohn <PERSON>e,<EMAIL>,1234567890';
      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'test.csv',
        mimetype: 'text/csv',
        size: csvContent.length
      } as MockFile;

      const headers = await CSVService.extractHeaders(mockFile as any);
      expect(headers).toEqual(['name', 'email', 'phone']);
    });

    it('should extract headers from an Excel file', async () => {
      // This test would need a real Excel file buffer
      // For unit testing purposes, we'll mock the Excel reading functionality
      jest.spyOn(CSVService as any, 'extractExcelHeaders').mockResolvedValue(['name', 'email', 'phone']);

      const mockFile = {
        buffer: Buffer.from('mock excel content'),
        originalname: 'test.xlsx',
        mimetype: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        size: 100
      } as MockFile;

      const headers = await CSVService.extractHeaders(mockFile as any);
      expect(headers).toEqual(['name', 'email', 'phone']);
    });

    it('should throw an error for unsupported file types', async () => {
      const mockFile = {
        buffer: Buffer.from('some content'),
        originalname: 'test.txt',
        mimetype: 'text/plain',
        size: 100
      } as MockFile;

      await expect(CSVService.extractHeaders(mockFile as any)).rejects.toThrow('Unsupported file type');
    });
  });

  describe('validateFileStructure', () => {
    it('should validate a valid CSV file', async () => {
      // Create a mock CSV file with multiple rows
      const csvContent = 'name,email,phone\nJohn Doe,<EMAIL>,1234567890\nJane Doe,<EMAIL>,0987654321';
      const mockFile = {
        buffer: Buffer.from(csvContent),
        originalname: 'test.csv',
        mimetype: 'text/csv',
        size: csvContent.length
      } as MockFile;

      // Mock the countCSVRows method to return a specific count
      jest.spyOn(CSVService as any, 'countCSVRows').mockResolvedValue(2);

      const result = await CSVService.validateFileStructure(mockFile as any);
      expect(result.isValid).toBe(true);
      expect(result.errors.length).toBe(0);
      expect(result.rowCount).toBe(2);
      expect(result.errorCode).toBeUndefined();
    });

    it('should reject a CSV file with too many rows', async () => {
      const mockFile = {
        buffer: Buffer.from('header1,header2\nvalue1,value2'),
        originalname: 'test.csv',
        mimetype: 'text/csv',
        size: 100
      } as MockFile;

      // Mock the countCSVRows method to return a count exceeding the limit
      jest.spyOn(CSVService as any, 'countCSVRows').mockResolvedValue(500000);

      const result = await CSVService.validateFileStructure(mockFile as any);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('too many rows'))).toBe(true);
      expect(result.errorCode).toBe(ERROR_CODES.TOO_MANY_ROWS);
    });

    it('should reject an Excel file with multiple sheets', async () => {
      const mockFile = {
        buffer: Buffer.from('mock excel content'),
        originalname: 'test.xlsx',
        mimetype: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        size: 100
      } as MockFile;

      // Mock the getExcelInfo method to return multiple sheets
      jest.spyOn(CSVService as any, 'getExcelInfo').mockReturnValue({ rowCount: 10, sheetCount: 2 });

      const result = await CSVService.validateFileStructure(mockFile as any);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('single sheet'))).toBe(true);
      expect(result.errorCode).toBe(ERROR_CODES.MULTIPLE_SHEETS);
    });

    it('should reject a file with no data or headers', async () => {
      const mockFile = {
        buffer: Buffer.from(''),
        originalname: 'test.csv',
        mimetype: 'text/csv',
        size: 0
      } as MockFile;

      // Mock the countCSVRows method to return 0 rows
      jest.spyOn(CSVService as any, 'countCSVRows').mockResolvedValue(0);

      const result = await CSVService.validateFileStructure(mockFile as any);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('no data or headers'))).toBe(true);
      expect(result.errorCode).toBe(ERROR_CODES.NO_HEADERS);
    });
  });
});

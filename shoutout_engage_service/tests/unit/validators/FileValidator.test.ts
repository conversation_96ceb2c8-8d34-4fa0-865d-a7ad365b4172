import { FileValidator } from '../../../validators/FileValidator';
import { ERROR_CODES, FILE_CONSTRAINTS } from '../../../lib/constant/file.constants';
import { MockResponseHelper } from '../../utils/testHelpers';

describe('FileValidator', () => {
  describe('handleErrors', () => {
    let mockReq: any;
    let mockRes: any;
    let mockNext: any;

    beforeEach(() => {
      mockReq = {};
      mockRes = MockResponseHelper.createMockResponse();
      mockNext = jest.fn();
    });

    it('should handle MulterError with LIMIT_FILE_SIZE code', () => {
      const multerError = {
        name: 'MulterError',
        code: 'LIMIT_FILE_SIZE',
        message: 'File too large'
      };

      FileValidator.handleErrors(multerError, mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        error: expect.stringContaining(`Maximum size is ${FILE_CONSTRAINTS.maxSizeBytes / (1024 * 1024)}MB`),
        errorCode: ERROR_CODES.FILE_TOO_LARGE,
        details: expect.arrayContaining([
          expect.objectContaining({
            code: ERROR_CODES.FILE_TOO_LARGE,
            message: expect.stringContaining('File too large'),
            field: 'file'
          })
        ])
      }));
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle other MulterError codes', () => {
      const multerError = {
        name: 'MulterError',
        code: 'SOME_OTHER_CODE',
        message: 'Some other error'
      };

      FileValidator.handleErrors(multerError, mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        error: 'Some other error',
        errorCode: 'FILE_ERROR'
      }));
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle custom errors with code', () => {
      const customError = {
        message: 'Invalid file extension',
        code: ERROR_CODES.INVALID_EXTENSION
      };

      FileValidator.handleErrors(customError, mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        error: 'Invalid file extension',
        errorCode: ERROR_CODES.INVALID_EXTENSION
      }));
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should call next() when no error is provided', () => {
      FileValidator.handleErrors(null, mockReq, mockRes, mockNext);

      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('validateFilePresence', () => {
    let mockReq: any;
    let mockRes: any;
    let mockNext: any;

    beforeEach(() => {
      mockReq = {};
      mockRes = MockResponseHelper.createMockResponse();
      mockNext = jest.fn();
    });

    it('should call next() when file is present', () => {
      mockReq.file = { originalname: 'test.csv' };

      FileValidator.validateFilePresence(mockReq, mockRes, mockNext);

      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalled();
    });

    it('should return 400 error when file is not present', () => {
      mockReq.file = undefined;

      FileValidator.validateFilePresence(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        error: 'No file uploaded',
        errorCode: 'FILE_MISSING'
      }));
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  // Note: We can't easily test getUploadMiddleware() and validateFileType() 
  // because they use multer which is difficult to mock.
  // These would be better tested through integration tests.
});
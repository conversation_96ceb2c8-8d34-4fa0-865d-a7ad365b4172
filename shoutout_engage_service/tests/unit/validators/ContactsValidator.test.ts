import { ContactsValidator } from '../../../validators/ContactsValidator';
import { contactFixtures } from '../../fixtures/contactFixtures';
import { bulkImportFixtures } from '../../fixtures/bulkImportFixtures';
import { ContactStatus } from '../../../types/contact.types';
import { MockResponseHelper } from '../../utils/testHelpers';
import { BadRequestError, ValidationError } from '../../../lib/errors/error-types';

describe('ContactsValidator', () => {
  describe('validateCreateContact', () => {
    describe('Valid data scenarios', () => {
      it('should validate complete valid contact data', () => {
        const { error, value } = ContactsValidator.validateCreateContact(
          contactFixtures.validContactRequest
        );

        expect(error).toBeUndefined();
        expect(value).toBeDefined();
        expect(value.name).toBe(contactFixtures.validContactRequest.name);
        expect(value.email).toBe(contactFixtures.validContactRequest.email.toLowerCase());
        expect(value.phone).toBe(contactFixtures.validContactRequest.phone);
        expect(value.status).toBe(ContactStatus.ACTIVE);
      });

      it('should validate minimal valid contact data', () => {
        const { error, value } = ContactsValidator.validateCreateContact(
          contactFixtures.minimalValidContactRequest
        );

        expect(error).toBeUndefined();
        expect(value).toBeDefined();
        expect(value.name).toBe(contactFixtures.minimalValidContactRequest.name);
        expect(value.email).toBe(contactFixtures.minimalValidContactRequest.email.toLowerCase());
        expect(value.phone).toBe(contactFixtures.minimalValidContactRequest.phone);
        expect(value.tags).toEqual([]);
        expect(value.additional_fields).toEqual({});
        expect(value.status).toBe(ContactStatus.ACTIVE);
      });

      it('should normalize email to lowercase', () => {
        const testData = {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890'
        };

        const { error, value } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeUndefined();
        expect(value.email).toBe('<EMAIL>');
      });

      it('should trim whitespace from string fields', () => {
        const testData = {
          name: '  John Doe  ',
          email: '  <EMAIL>  ',
          phone: '  +1234567890  ',
          country: '  United States  '
        };

        const { error, value } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeUndefined();
        expect(value.name).toBe('John Doe');
        expect(value.email).toBe('<EMAIL>');
        expect(value.phone).toBe('+1234567890');
        expect(value.country).toBe('United States');
      });

      it('should validate all contact status values', () => {
        const statusValues = Object.values(ContactStatus);

        statusValues.forEach(status => {
          const testData = {
            name: 'Test User',
            email: '<EMAIL>',
            phone: '+1234567890',
            status
          };

          const { error } = ContactsValidator.validateCreateContact(testData);
          expect(error).toBeUndefined();
        });
      });
    });

    describe('Required field validation', () => {
      it('should reject missing name', () => {
        const { error } = ContactsValidator.validateCreateContact(
          contactFixtures.invalidContactRequests.missingName
        );

        expect(error).toBeDefined();
        expect(error?.details).toHaveLength(1);
        expect(error?.details[0].path).toEqual(['name']);
        expect(error?.details[0].message).toContain('required');
      });

      it('should reject missing email', () => {
        const { error } = ContactsValidator.validateCreateContact(
          contactFixtures.invalidContactRequests.missingEmail
        );

        expect(error).toBeDefined();
        expect(error?.details).toHaveLength(1);
        expect(error?.details[0].path).toEqual(['email']);
        expect(error?.details[0].message).toContain('required');
      });

      it('should reject missing phone', () => {
        const { error } = ContactsValidator.validateCreateContact(
          contactFixtures.invalidContactRequests.missingPhone
        );

        expect(error).toBeDefined();
        expect(error?.details).toHaveLength(1);
        expect(error?.details[0].path).toEqual(['phone']);
        expect(error?.details[0].message).toContain('required');
      });

      it('should reject empty name', () => {
        const { error } = ContactsValidator.validateCreateContact(
          contactFixtures.invalidContactRequests.emptyName
        );

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['name']);
        expect(error?.details[0].message).toContain('Name is required');
      });
    });

    describe('Email validation', () => {
      it('should reject invalid email format', () => {
        const { error } = ContactsValidator.validateCreateContact(
          contactFixtures.invalidContactRequests.invalidEmail
        );

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['email']);
        expect(error?.details[0].message).toContain('valid email address');
      });

      it('should reject email without domain', () => {
        const testData = {
          name: 'Test User',
          email: 'test@',
          phone: '+1234567890'
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['email']);
      });

      it('should reject email without @ symbol', () => {
        const testData = {
          name: 'Test User',
          email: 'testexample.com',
          phone: '+1234567890'
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['email']);
      });
    });

    describe('Phone validation', () => {
      it('should accept various phone formats', () => {
        const validPhones = [
          '+1234567890',
          '1234567890',
          '******-567-890',
          '+1 (234) 567-890',
          '(234) 567-890'
        ];

        validPhones.forEach(phone => {
          const testData = {
            name: 'Test User',
            email: '<EMAIL>',
            phone
          };

          const { error } = ContactsValidator.validateCreateContact(testData);
          expect(error).toBeUndefined();
        });
      });

      it('should reject phone with invalid characters', () => {
        const testData = {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+123abc456'
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['phone']);
        expect(error?.details[0].message).toContain('numbers, spaces, hyphens');
      });

      it('should reject phone too short', () => {
        const testData = {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '123'
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['phone']);
        expect(error?.details[0].message).toContain('at least 7 characters');
      });

      it('should reject phone too long', () => {
        const testData = {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+123456789012345678901'
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['phone']);
        expect(error?.details[0].message).toContain('not exceed 20 characters');
      });
    });

    describe('Optional field validation', () => {
      it('should validate country field', () => {
        const testData = {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          country: 'A'.repeat(101) // Too long
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['country']);
        expect(error?.details[0].message).toContain('not exceed 100 characters');
      });

      it('should validate country_code format', () => {
        const { error } = ContactsValidator.validateCreateContact(
          contactFixtures.invalidContactRequests.invalidCountryCode
        );

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['country_code']);
        expect(error?.details[0].message).toContain('2-letter uppercase ISO code');
      });

      it('should validate avatar_url format', () => {
        const { error } = ContactsValidator.validateCreateContact(
          contactFixtures.invalidContactRequests.invalidAvatarUrl
        );

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['avatar_url']);
        expect(error?.details[0].message).toContain('must be a valid uri with a scheme matching the http|https pattern');
      });

      it('should accept valid avatar URLs', () => {
        const validUrls = [
          'https://example.com/avatar.jpg',
          'http://example.com/avatar.png',
          'https://cdn.example.com/images/avatar.gif'
        ];

        validUrls.forEach(avatar_url => {
          const testData = {
            name: 'Test User',
            email: '<EMAIL>',
            phone: '+1234567890',
            avatar_url
          };

          const { error } = ContactsValidator.validateCreateContact(testData);
          expect(error).toBeUndefined();
        });
      });
    });

    describe('Tags validation', () => {
      it('should validate tag structure', () => {
        const testData = {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [
            {
              tag_id: 'invalid-object-id',
              tag_name: 'test'
            }
          ]
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['tags', 0, 'tag_id']);
        expect(error?.details[0].message).toContain('valid MongoDB ObjectId');
      });

      it('should validate tag_name length', () => {
        const testData = {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [
            {
              tag_id: '507f1f77bcf86cd799439011',
              tag_name: 'A'.repeat(101) // Too long
            }
          ]
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['tags', 0, 'tag_name']);
        expect(error?.details[0].message).toContain('not exceed 100 characters');
      });

      it('should reject empty tag_name', () => {
        const testData = {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [
            {
              tag_id: '507f1f77bcf86cd799439011',
              tag_name: ''
            }
          ]
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['tags', 0, 'tag_name']);
        expect(error?.details[0].message).toContain('Tag name is required');
      });
    });

    describe('Additional fields validation', () => {
      it('should validate additional_fields structure', () => {
        const testData = {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          additional_fields: {
            company: 'Test Company',
            age: 30,
            is_active: true,
            invalid_field: { nested: 'object' } // Invalid type
          }
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details[0].path).toEqual(['additional_fields', 'invalid_field']);
      });

      it('should accept valid additional_fields types', () => {
        const testData = {
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          additional_fields: {
            company: 'Test Company',
            age: 30,
            is_premium: true,
            score: 95.5
          }
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeUndefined();
      });
    });

    describe('Multiple validation errors', () => {
      it('should return all validation errors', () => {
        const testData = {
          name: '',
          email: 'invalid-email',
          phone: '123', // Too short
          country_code: 'INVALID'
        };

        const { error } = ContactsValidator.validateCreateContact(testData);

        expect(error).toBeDefined();
        expect(error?.details).toHaveLength(4);
        
        const errorPaths = error?.details.map(detail => detail.path.join('.'));
        expect(errorPaths).toContain('name');
        expect(errorPaths).toContain('email');
        expect(errorPaths).toContain('phone');
        expect(errorPaths).toContain('country_code');
      });
    });
  });

  describe('validateCreateContactMiddleware', () => {
    let mockReq: any;
    let mockRes: any;
    let mockNext: any;

    beforeEach(() => {
      mockReq = {
        body: {}
      };
      mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      mockNext = jest.fn();
    });

    it('should call next() for valid data', () => {
      mockReq.body = contactFixtures.validContactRequest;

      ContactsValidator.validateCreateContactMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should throw ValidationError for invalid data', () => {
      mockReq.body = contactFixtures.invalidContactRequests.missingName;

      // Should throw ValidationError
      expect(() => {
        ContactsValidator.validateCreateContactMiddleware(mockReq, mockRes, mockNext);
      }).toThrow(ValidationError);
      
      // Try to catch the error to check its properties
      try {
        ContactsValidator.validateCreateContactMiddleware(mockReq, mockRes, mockNext);
      } catch (error: any) {
        // Verify it's a ValidationError
        expect(error).toBeInstanceOf(ValidationError);
        // Check error properties
        expect(error.message).toBe('Validation failed for contact creation');
        expect(error.errorCode).toBe('CONTACT_VALIDATION_001');
        expect(error.details).toEqual(expect.arrayContaining([
          expect.objectContaining({
            field: 'name',
            message: expect.stringContaining('required')
          })
        ]));
      }
      
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should sanitize and replace request body with validated data', () => {
      mockReq.body = {
        ...contactFixtures.validContactRequest,
        extraField: 'should be removed'
      };

      ContactsValidator.validateCreateContactMiddleware(mockReq, mockRes, mockNext);

      expect(mockReq.body).not.toHaveProperty('extraField');
      expect(mockReq.body.email).toBe(contactFixtures.validContactRequest.email.toLowerCase());
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('validateGetContacts', () => {
    // Test valid pagination parameters
    test('should validate valid pagination parameters', () => {
      const queryParams = {
        page: 2,
        page_size: 50
      };
      
      const { error, value } = ContactsValidator.validateGetContacts(queryParams);
      
      expect(error).toBeUndefined();
      expect(value).toEqual({
        page: 2,
        page_size: 50,
        sort_by: 'created_at',
        sort_direction: 'desc'
      });
    });
    
    // Test missing pagination parameters
    test('should reject when pagination parameters are not provided', () => {
      const queryParams = {};
      
      const { error } = ContactsValidator.validateGetContacts(queryParams);
      
      expect(error).toBeDefined();
      expect(error?.details).toHaveLength(2);
      expect(error?.details.some(d => d.message.includes('Page is required'))).toBe(true);
      expect(error?.details.some(d => d.message.includes('Page size is required'))).toBe(true);
    });
    
    // Test missing page parameter
    test('should reject when page parameter is missing', () => {
      const queryParams = {
        page_size: 20
      };
      
      const { error } = ContactsValidator.validateGetContacts(queryParams);
      
      expect(error).toBeDefined();
      expect(error?.details).toHaveLength(1);
      expect(error?.details[0].message).toContain('Page is required');
    });
    
    // Test missing page_size parameter
    test('should reject when page_size parameter is missing', () => {
      const queryParams = {
        page: 1
      };
      
      const { error } = ContactsValidator.validateGetContacts(queryParams);
      
      expect(error).toBeDefined();
      expect(error?.details).toHaveLength(1);
      expect(error?.details[0].message).toContain('Page size is required');
    });
    
    // Test invalid pagination parameters
    test('should reject invalid pagination parameters', () => {
      const queryParams = {
        page: 0,
        page_size: 200
      };
      
      const { error } = ContactsValidator.validateGetContacts(queryParams);
      
      expect(error).toBeDefined();
      expect(error?.details).toHaveLength(2);
      expect(error?.details[0].message).toContain('Page must be at least 1');
      expect(error?.details[1].message).toContain('Page size cannot exceed 100');
    });
    
    // Test valid sorting parameters
    test('should validate valid sorting parameters', () => {
      const queryParams = {
        page: 1,
        page_size: 20,
        sort_by: 'name',
        sort_direction: 'asc'
      };
      
      // Use the schema directly to test validation
      const { error, value } = ContactsValidator.getContactsSchema.validate(queryParams);
      
      expect(error).toBeUndefined();
      expect(value).toEqual({
        page: 1,
        page_size: 20,
        sort_by: 'name',
        sort_direction: 'asc'
      });
    });
    
    // Test invalid sorting parameters
    test('should reject invalid sorting parameters', () => {
      const queryParams = {
        page: 1,
        page_size: 20,
        sort_by: 'invalid_field',
        sort_direction: 'random'
      };
      
      // Use the schema directly to test validation
      const { error } = ContactsValidator.getContactsSchema.validate(queryParams);
      
      expect(error).toBeDefined();
      expect(error?.details).toHaveLength(2);
      expect(error?.details[0].message).toContain('Sort field must be one of');
      expect(error?.details[1].message).toContain('Sort direction must be either asc or desc');
    });
    
    // Test filtering via contactFilterQuery is covered in tests below
    
    // Test valid search parameter
    test('should validate valid search parameter', () => {
      const queryParams = {
        page: 1,
        page_size: 20,
        search: 'john doe'
      };
      
      // Use the schema directly to test validation
      const { error, value } = ContactsValidator.getContactsSchema.validate(queryParams);
      
      expect(error).toBeUndefined();
      expect(value.search).toBe('john doe');
    });
    
    // Test empty search parameter
    test('should reject empty search parameter', () => {
      const queryParams = {
        page: 1,
        page_size: 20,
        search: ''
      };
      
      // Validation should still work for valid cases, just throw errors for invalid ones
      const { error } = ContactsValidator.getContactsSchema.validate(queryParams);
      
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('is not allowed to be empty');
    });
    
    // Test valid contactFilterQuery
    test('should validate valid contactFilterQuery', () => {
      const mongoQuery = {
        "$and": [
          { "firstName": { "$regex": "^Stev" } },
          { "lastName": { "$in": ["Vai", "Vaughan"] } },
          { "age": { "$gt": "28" } }
        ]
      };
      
      const queryParams = {
        page: 1,
        page_size: 20,
        contactFilterQuery: JSON.stringify(mongoQuery)
      };
      
      // Use the schema directly to test validation
      const { error, value } = ContactsValidator.getContactsSchema.validate(queryParams);
      
      expect(error).toBeUndefined();
      // The contactFilterQuery is parsed into an object by Joi
      expect(value.contactFilterQuery).toEqual(mongoQuery);
    });
    
    // Test invalid contactFilterQuery - not valid JSON
    test('should throw BadRequestError for invalid contactFilterQuery JSON', () => {
      const queryParams = {
        contactFilterQuery: '{invalid json'
      };
      
      // Should throw BadRequestError
      expect(() => {
        ContactsValidator.validateGetContacts(queryParams);
      }).toThrow(BadRequestError);
      
      // Try to catch the error to check its properties
      try {
        ContactsValidator.validateGetContacts(queryParams);
      } catch (error: any) {
        expect(error.message).toBe('Invalid contact filter query format');
        expect(error.errorCode).toBe('QUERY_001');
        expect(error.details).toEqual([{
          field: 'contactFilterQuery',
          message: 'Contact filter query must be a valid JSON string'
        }]);
      }
    });
    
    // Test invalid contactFilterQuery - not an object
    test('should reject invalid contactFilterQuery - not an object', () => {
      const queryParams = {
        page: 1,
        page_size: 20,
        contactFilterQuery: JSON.stringify("not an object")
      };
      
      // Use the schema directly to test validation
      const { error } = ContactsValidator.getContactsSchema.validate(queryParams);
      
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('Contact filter query must be a valid MongoDB query object');
    });
    
    // Test complex contactFilterQuery
    test('should validate complex contactFilterQuery', () => {
      const mongoQuery = {
        "$and": [
          { "firstName": { "$regex": "^Stev" } },
          { "lastName": { "$in": ["Vai", "Vaughan"] } },
          { "age": { "$gt": "28" } },
          { "$or": [
            { "isMusician": true },
            { "instrument": "Guitar" }
          ]},
          { "$eq": ["$groupedField1", "$groupedField4"] },
          { "birthdate": { "$gte": "1954-10-03", "$lte": "1960-06-06" } }
        ]
      };
      
      const queryParams = {
        page: 1,
        page_size: 20,
        contactFilterQuery: JSON.stringify(mongoQuery)
      };
      
      // Use the schema directly to test validation
      const { error, value } = ContactsValidator.getContactsSchema.validate(queryParams);
      
      expect(error).toBeUndefined();
      // The contactFilterQuery is parsed into an object by Joi
      expect(value.contactFilterQuery).toEqual(mongoQuery);
    });
  });
  
  describe('validateGetContactsMiddleware', () => {
    test('should call next() for valid query parameters', () => {
      const req = MockResponseHelper.createMockRequest({}, {}, {
        page: 2,
        page_size: 50
      });
      const res = MockResponseHelper.createMockResponse();
      const next = jest.fn();
      
      ContactsValidator.validateGetContactsMiddleware(req, res, next);
      
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
      expect(req.query).toEqual({
        page: 2,
        page_size: 50,
        sort_by: 'created_at',
        sort_direction: 'desc'
      });
    });
    
    test('should throw ValidationError for invalid query parameters', () => {
      const req = MockResponseHelper.createMockRequest({}, {}, {
        page: 0,
        page_size: 200
      });
      const res = MockResponseHelper.createMockResponse();
      const next = jest.fn();
      
      // Should throw ValidationError with specific properties
      try {
        ContactsValidator.validateGetContactsMiddleware(req, res, next);
        // If we get here, the test should fail because no error was thrown
        fail('Expected ValidationError to be thrown');
      } catch (error: any) {
        // Verify it's a ValidationError
        expect(error).toBeInstanceOf(ValidationError);
        // Check error properties
        expect(error.message).toBe('Invalid query parameters for contacts retrieval');
        expect(error.errorCode).toBe('CONTACT_QUERY_VALIDATION_001');
        expect(error.details).toEqual(expect.arrayContaining([
          expect.objectContaining({
            field: 'page',
            message: 'Page must be at least 1'
          }),
          expect.objectContaining({
            field: 'page_size',
            message: 'Page size cannot exceed 100'
          })
        ]));
      }
      
      // Verify next was not called
      expect(next).not.toHaveBeenCalled();
    });
    
    test('should parse contactFilterQuery JSON to object', () => {
      const mongoQuery = {
        "$and": [
          { "firstName": { "$regex": "^Stev" } },
          { "lastName": { "$in": ["Vai", "Vaughan"] } }
        ]
      };
      
      const req = MockResponseHelper.createMockRequest({}, {}, {
        page: 1,
        page_size: 20,
        contactFilterQuery: JSON.stringify(mongoQuery)
      });
      const res = MockResponseHelper.createMockResponse();
      const next = jest.fn();
      
      ContactsValidator.validateGetContactsMiddleware(req, res, next);
      
      expect(next).toHaveBeenCalled();
      // After validation, contactFilterQuery should be parsed into an object
      expect(req.query.contactFilterQuery).toEqual(mongoQuery);
    });
    
    test('should throw BadRequestError for invalid contactFilterQuery JSON', () => {
      const req = MockResponseHelper.createMockRequest({}, {}, {
        contactFilterQuery: '{invalid json'
      });
      const res = MockResponseHelper.createMockResponse();
      const next = jest.fn();
      
      // Should throw BadRequestError with specific properties
      try {
        ContactsValidator.validateGetContactsMiddleware(req, res, next);
        // If we get here, the test should fail because no error was thrown
        fail('Expected BadRequestError to be thrown');
      } catch (error: any) {
        // Verify it's a BadRequestError
        expect(error).toBeInstanceOf(BadRequestError);
        // Check error properties
        expect(error.message).toBe('Invalid contact filter query format');
        expect(error.errorCode).toBe('QUERY_001');
        expect(error.details).toEqual([{
          field: 'contactFilterQuery',
          message: 'Contact filter query must be a valid JSON string'
        }]);
      }
      
      // Verify next was not called
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('validateGetContactById', () => {
    test('should validate a valid contact ID', () => {
      const params = {
        id: '507f1f77bcf86cd799439011' // Valid MongoDB ObjectId
      };
      
      const { error, value } = ContactsValidator.validateGetContactById(params);
      
      expect(error).toBeUndefined();
      expect(value).toEqual(params);
    });
    
    test('should reject an invalid contact ID format', () => {
      const params = {
        id: 'invalid-id' // Not a valid MongoDB ObjectId
      };
      
      const { error } = ContactsValidator.validateGetContactById(params);
      
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('Contact ID must be a valid MongoDB ObjectId');
    });
    
    test('should reject a missing contact ID', () => {
      const params = {};
      
      const { error } = ContactsValidator.validateGetContactById(params);
      
      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('Contact ID is required');
    });
  });
  
  describe('validateGetContactByIdMiddleware', () => {
    test('should call next() for valid contact ID', () => {
      const req = MockResponseHelper.createMockRequest({}, {
        id: '507f1f77bcf86cd799439011' // Valid MongoDB ObjectId
      });
      const res = MockResponseHelper.createMockResponse();
      const next = jest.fn();
      
      ContactsValidator.validateGetContactByIdMiddleware(req, res, next);
      
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
      expect(req.params).toEqual({
        id: '507f1f77bcf86cd799439011'
      });
    });
    
    test('should throw ValidationError for invalid contact ID format', () => {
      const req = MockResponseHelper.createMockRequest({}, {
        id: 'invalid-id' // Not a valid MongoDB ObjectId
      });
      const res = MockResponseHelper.createMockResponse();
      const next = jest.fn();
      
      try {
        ContactsValidator.validateGetContactByIdMiddleware(req, res, next);
        fail('Expected ValidationError to be thrown');
      } catch (error: any) {
        expect(error).toBeInstanceOf(ValidationError);
        expect(error.message).toBe('Invalid contact ID');
        expect(error.errorCode).toBe('CONTACT_ID_VALIDATION_001');
        expect(error.details).toEqual(expect.arrayContaining([
          expect.objectContaining({
            field: 'id',
            message: 'Contact ID must be a valid MongoDB ObjectId (24 hexadecimal characters)'
          })
        ]));
      }
      
      expect(next).not.toHaveBeenCalled();
    });
    
    test('should throw ValidationError for missing contact ID', () => {
      const req = MockResponseHelper.createMockRequest({}, {});
      const res = MockResponseHelper.createMockResponse();
      const next = jest.fn();
      
      try {
        ContactsValidator.validateGetContactByIdMiddleware(req, res, next);
        fail('Expected ValidationError to be thrown');
      } catch (error: any) {
        expect(error).toBeInstanceOf(ValidationError);
        expect(error.message).toBe('Invalid contact ID');
        expect(error.errorCode).toBe('CONTACT_ID_VALIDATION_001');
        expect(error.details).toEqual(expect.arrayContaining([
          expect.objectContaining({
            field: 'id',
            message: 'Contact ID is required'
          })
        ]));
      }
      
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('validateBulkImport', () => {
    test('should validate valid bulk import request with all fields', () => {
      const { error, value } = ContactsValidator.validateBulkImport(bulkImportFixtures.validBulkImportRequest);
      
      expect(error).toBeUndefined();
      expect(value).toEqual(bulkImportFixtures.validBulkImportRequest);
    });
    
    test('should validate minimal valid bulk import request', () => {
      const { error, value } = ContactsValidator.validateBulkImport(bulkImportFixtures.minimalValidBulkImportRequest);
      
      expect(error).toBeUndefined();
      expect(value).toEqual(bulkImportFixtures.minimalValidBulkImportRequest);
    });
    
    test('should reject when fieldMappings is missing', () => {
      const { error } = ContactsValidator.validateBulkImport(bulkImportFixtures.invalidBulkImportRequests.missingFieldMappings);
      
      expect(error).toBeDefined();
      expect(error?.details).toHaveLength(1);
      expect(error?.details[0].message).toContain('Field mappings are required');
    });
    
    test('should reject when fieldMappings is empty', () => {
      const { error } = ContactsValidator.validateBulkImport(bulkImportFixtures.invalidBulkImportRequests.emptyFieldMappings);
      
      expect(error).toBeDefined();
      expect(error?.details).toHaveLength(1);
      expect(error?.details[0].message).toContain('At least one field mapping is required');
    });
    
    test('should reject when required field is not mapped', () => {
      const { error } = ContactsValidator.validateBulkImport(bulkImportFixtures.invalidBulkImportRequests.missingRequiredField);
      
      expect(error).toBeDefined();
      expect(error?.details).toHaveLength(1);
      expect(error?.details[0].message).toContain("Required field 'phone' must be mapped");
    });
    
    test('should reject when fileColumnName is missing', () => {
      const { error } = ContactsValidator.validateBulkImport(bulkImportFixtures.invalidBulkImportRequests.missingFileColumnName);
      
      expect(error).toBeDefined();
      expect(error?.details).toHaveLength(1);
      expect(error?.details[0].message).toContain('File column name is required');
    });
    
    test('should reject when systemAttributeName is missing', () => {
      const { error } = ContactsValidator.validateBulkImport(bulkImportFixtures.invalidBulkImportRequests.missingSystemAttributeName);
      
      expect(error).toBeDefined();
      expect(error?.details).toHaveLength(1);
      expect(error?.details[0].message).toContain('System attribute name is required');
    });
  });
  
  describe('validateBulkImportMiddleware', () => {
    let mockReq: any;
    let mockRes: any;
    let mockNext: any;
    
    beforeEach(() => {
      mockReq = {
        body: {}
      };
      mockRes = MockResponseHelper.createMockResponse();
      mockNext = jest.fn();
    });
    
    test('should call next() for valid data', () => {
      mockReq.body = bulkImportFixtures.validBulkImportRequest;
      
      ContactsValidator.validateBulkImportMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
    
    test('should throw ValidationError for invalid data', () => {
      mockReq.body = bulkImportFixtures.invalidBulkImportRequests.missingFieldMappings;
      
      // Should throw ValidationError
      expect(() => {
        ContactsValidator.validateBulkImportMiddleware(mockReq, mockRes, mockNext);
      }).toThrow(ValidationError);
      
      // Try to catch the error to check its properties
      try {
        ContactsValidator.validateBulkImportMiddleware(mockReq, mockRes, mockNext);
      } catch (error: any) {
        // Verify it's a ValidationError
        expect(error).toBeInstanceOf(ValidationError);
        // Check error properties
        expect(error.message).toBe('Validation failed for contact bulk import');
        expect(error.errorCode).toBe('CONTACT_BULK_IMPORT_VALIDATION_001');
        expect(error.details).toEqual(expect.arrayContaining([
          expect.objectContaining({
            field: 'fieldMappings',
            message: expect.stringContaining('required')
          })
        ]));
      }
      
      expect(mockNext).not.toHaveBeenCalled();
    });
    
    test('should throw ValidationError when required field is not mapped', () => {
      mockReq.body = bulkImportFixtures.invalidBulkImportRequests.missingRequiredField;
      
      try {
        ContactsValidator.validateBulkImportMiddleware(mockReq, mockRes, mockNext);
        fail('Expected ValidationError to be thrown');
      } catch (error: any) {
        expect(error).toBeInstanceOf(ValidationError);
        expect(error.message).toBe('Validation failed for contact bulk import');
        expect(error.errorCode).toBe('CONTACT_BULK_IMPORT_VALIDATION_001');
        expect(error.details).toEqual(expect.arrayContaining([
          expect.objectContaining({
            field: 'fieldMappings',
            message: expect.stringContaining("Required field 'phone' must be mapped")
          })
        ]));
      }
      
      expect(mockNext).not.toHaveBeenCalled();
    });
    
    test('should sanitize and replace request body with validated data', () => {
      mockReq.body = {
        ...bulkImportFixtures.validBulkImportRequest,
        extraField: 'should be removed'
      };
      
      ContactsValidator.validateBulkImportMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockReq.body).not.toHaveProperty('extraField');
      expect(mockNext).toHaveBeenCalled();
    });
  });
});
import { ContactsHandler } from '../../../handlers/ContactsHandler';
import { ContactDAO } from '../../../lib/db/dao/ContactDAO';
import { ContactStatus } from '../../../types/contact.types';
import { contactFixtures, organizationFixtures } from '../../fixtures/contactFixtures';
import { bulkImportFixtures } from '../../fixtures/bulkImportFixtures';
import { fileFixtures } from '../../fixtures/fileFixtures';
import { MockResponseHelper } from '../../utils/testHelpers';
import { ObjectId } from 'mongodb';
import { ValidationError, NotFoundError, BadRequestError, DatabaseError, OrganizationError } from '../../../lib/errors/error-types';
import { CSVService } from '../../../services/csv.service';
import { contactBulkImportQueue } from '../../../lib/queue/queue';
import { SchemaExtractor } from '../../../lib/utils/schemaExtractor';
import { ERROR_CODES } from '../../../lib/constant/file.constants';

// Mock the ContactDAO
jest.mock('../../../lib/db/dao/ContactDAO');
const mockContactDAO = ContactDAO as jest.Mocked<typeof ContactDAO>;

// Mock the formatContactResponse method
mockContactDAO.formatContactResponse = jest.fn().mockImplementation((contact) => {
  if (!contact) return undefined;
  
  return {
    _id: contact._id.toString(),
    org_id: contact.org_id,
    created_by: contact.created_by,
    name: contact.name,
    email: contact.email,
    phone: contact.phone,
    country: contact.country,
    country_code: contact.country_code,
    avatar_url: contact.avatar_url,
    tags: contact.tags.map((tag: any) => ({
      tag_id: tag.tag_id.toString(),
      tag_name: tag.tag_name
    })),
    additional_fields: contact.additional_fields,
    status: contact.status,
    created_at: contact.created_at,
    updated_at: contact.updated_at
  };
});

// For getContacts tests
mockContactDAO.getContacts = jest.fn();
mockContactDAO.formatContactsResponse = jest.fn();

// Mock the logger
jest.mock('../../../lib/logger', () => ({
  logger: () => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })
}));

// Mock the CSVService
jest.mock('../../../services/csv.service');
const mockCSVService = CSVService as jest.Mocked<typeof CSVService>;

// Mock the contactBulkImportQueue
jest.mock('../../../lib/queue/queue', () => ({
  contactBulkImportQueue: {
    add: jest.fn().mockResolvedValue({}),
  }
}));

// Mock SchemaExtractor
const mockContactFields = [
  { name: 'name', type: 'string', required: true, description: 'Contact name' },
  { name: 'email', type: 'string', required: true, description: 'Contact email' },
  { name: 'phone', type: 'string', required: true, description: 'Contact phone number' }
];

jest.mock('../../../lib/utils/schemaExtractor', () => ({
  SchemaExtractor: {
    getContactFields: jest.fn().mockReturnValue(mockContactFields)
  }
}));

describe('ContactsHandler', () => {
  // Reset all mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset mock implementations
    mockCSVService.validateFileStructure = jest.fn();
    mockCSVService.extractHeaders = jest.fn();
    (contactBulkImportQueue.add as jest.Mock).mockReset().mockResolvedValue({});
    mockContactDAO.createBulkImportJob.mockReset();
    
    // Reset SchemaExtractor mock
    (SchemaExtractor.getContactFields as jest.Mock).mockReturnValue(mockContactFields);
  });
  describe('createContact', () => {
    let mockReq: any;
    let mockRes: any;

    beforeEach(() => {
      mockReq = {
        userId: organizationFixtures.sampleUser.id,
        organizationId: organizationFixtures.sampleOrganization.id,
        body: { ...contactFixtures.validContactRequest }
      };
      mockRes = MockResponseHelper.createMockResponse();
      
      // Reset all mocks
      jest.clearAllMocks();
    });

    describe('Successful contact creation', () => {
      it('should create contact with complete data', async () => {
        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: contactFixtures.validContactRequest.name,
          email: contactFixtures.validContactRequest.email,
          phone: contactFixtures.validContactRequest.phone,
          country: contactFixtures.validContactRequest.country,
          country_code: contactFixtures.validContactRequest.country_code,
          avatar_url: undefined,
          tags: contactFixtures.validContactRequest.tags?.map(tag => ({
            tag_id: new ObjectId(tag.tag_id),
            tag_name: tag.tag_name
          })) || [],
          additional_fields: contactFixtures.validContactRequest.additional_fields || {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockContactDAO.createContact).toHaveBeenCalledWith({
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: contactFixtures.validContactRequest.name,
          email: contactFixtures.validContactRequest.email,
          phone: contactFixtures.validContactRequest.phone,
          country: contactFixtures.validContactRequest.country,
          country_code: contactFixtures.validContactRequest.country_code,
          avatar_url: contactFixtures.validContactRequest.avatar_url,
          tags: expect.arrayContaining([
            expect.objectContaining({
              tag_id: expect.any(ObjectId),
              tag_name: expect.any(String)
            })
          ]),
          additional_fields: contactFixtures.validContactRequest.additional_fields,
          status: ContactStatus.ACTIVE
        });

        expect(mockRes.status).toHaveBeenCalledWith(201);
        expect(mockRes.json).toHaveBeenCalledWith({
          _id: mockCreatedContact._id.toString(),
          org_id: mockCreatedContact.org_id,
          created_by: mockCreatedContact.created_by,
          name: mockCreatedContact.name,
          email: mockCreatedContact.email,
          phone: mockCreatedContact.phone,
          country: mockCreatedContact.country,
          country_code: mockCreatedContact.country_code,
          avatar_url: mockCreatedContact.avatar_url,
          tags: mockCreatedContact.tags.map((tag: any) => ({
            tag_id: tag.tag_id.toString(),
            tag_name: tag.tag_name
          })),
          additional_fields: mockCreatedContact.additional_fields,
          status: mockCreatedContact.status,
          created_at: mockCreatedContact.created_at,
          updated_at: mockCreatedContact.updated_at
        });
      });

      it('should create contact with minimal data', async () => {
        mockReq.body = { ...contactFixtures.minimalValidContactRequest };

        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: contactFixtures.minimalValidContactRequest.name,
          email: contactFixtures.minimalValidContactRequest.email,
          phone: contactFixtures.minimalValidContactRequest.phone,
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockContactDAO.createContact).toHaveBeenCalledWith({
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: contactFixtures.minimalValidContactRequest.name,
          email: contactFixtures.minimalValidContactRequest.email,
          phone: contactFixtures.minimalValidContactRequest.phone,
          country: undefined,
          country_code: undefined,
          avatar_url: undefined,
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE
        });

        expect(mockRes.status).toHaveBeenCalledWith(201);
      });

      it('should handle tags conversion from string to ObjectId', async () => {
        const tagId = new ObjectId().toString();
        mockReq.body = {
          ...contactFixtures.minimalValidContactRequest,
          tags: [
            {
              tag_id: tagId,
              tag_name: 'test-tag'
            }
          ]
        };

        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: mockReq.body.name,
          email: mockReq.body.email,
          phone: mockReq.body.phone,
          tags: [
            {
              tag_id: new ObjectId(tagId),
              tag_name: 'test-tag'
            }
          ],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockContactDAO.createContact).toHaveBeenCalledWith(
          expect.objectContaining({
            tags: [
              {
                tag_id: expect.any(ObjectId),
                tag_name: 'test-tag'
              }
            ]
          })
        );
      });

      it('should use default status when not provided', async () => {
        mockReq.body = { ...contactFixtures.minimalValidContactRequest };
        delete mockReq.body.status;

        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: mockReq.body.name,
          email: mockReq.body.email,
          phone: mockReq.body.phone,
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockContactDAO.createContact).toHaveBeenCalledWith(
          expect.objectContaining({
            status: ContactStatus.ACTIVE
          })
        );
      });
    });

    describe('Error handling', () => {
      it('should return 400 when organization context is missing', async () => {
        mockReq.organizationId = undefined;

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.status).toHaveBeenCalledWith(400);
        expect(mockRes.json).toHaveBeenCalledWith({
          error: 'Organization context is required',
          errorCode: 'ORG_001'
        });
        expect(mockContactDAO.createContact).not.toHaveBeenCalled();
      });

      it('should handle duplicate contact error', async () => {
        const duplicateError = new Error('Contact with email already exists in organization');
        mockContactDAO.createContact.mockRejectedValue(duplicateError);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.status).toHaveBeenCalledWith(409);
        expect(mockRes.json).toHaveBeenCalledWith({
          error: 'Contact with email already exists in organization',
          errorCode: 'CONTACT_001'
        });
      });

      it('should handle validation errors', async () => {
        const validationError = new Error('Validation failed: Invalid tag reference');
        mockContactDAO.createContact.mockRejectedValue(validationError);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.status).toHaveBeenCalledWith(422);
        expect(mockRes.json).toHaveBeenCalledWith({
          error: 'Validation failed: Invalid tag reference',
          errorCode: 'CONTACT_002'
        });
      });

      it('should handle organization-related errors', async () => {
        const orgError = new Error('Invalid organization context');
        mockContactDAO.createContact.mockRejectedValue(orgError);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.status).toHaveBeenCalledWith(400);
        expect(mockRes.json).toHaveBeenCalledWith({
          error: 'Invalid organization context',
          errorCode: 'ORG_002'
        });
      });

      it('should handle database errors', async () => {
        const dbError = new Error('Database connection failed');
        mockContactDAO.createContact.mockRejectedValue(dbError);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.status).toHaveBeenCalledWith(500);
        expect(mockRes.json).toHaveBeenCalledWith({
          error: 'Database operation failed',
          errorCode: 'DB_001'
        });
      });

      it('should handle generic errors', async () => {
        const genericError = new Error('Something unexpected happened');
        mockContactDAO.createContact.mockRejectedValue(genericError);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.status).toHaveBeenCalledWith(500);
        expect(mockRes.json).toHaveBeenCalledWith({
          error: 'Internal server error',
          errorCode: 'INTERNAL_001'
        });
      });

      it('should handle non-Error objects', async () => {
        const nonErrorObject = 'String error';
        mockContactDAO.createContact.mockRejectedValue(nonErrorObject);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.status).toHaveBeenCalledWith(500);
        expect(mockRes.json).toHaveBeenCalledWith({
          error: 'Internal server error',
          errorCode: 'INTERNAL_001'
        });
      });
    });

    describe('Response formatting', () => {
      it('should properly format ObjectIds in response', async () => {
        const tagObjectId = new ObjectId();
        const contactObjectId = new ObjectId();

        const mockCreatedContact = {
          _id: contactObjectId,
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [
            {
              tag_id: tagObjectId,
              tag_name: 'test-tag'
            }
          ],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.json).toHaveBeenCalledWith(
          expect.objectContaining({
            _id: contactObjectId.toString(),
            tags: [
              {
                tag_id: tagObjectId.toString(),
                tag_name: 'test-tag'
              }
            ]
          })
        );
      });

      it('should handle empty tags array', async () => {
        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.json).toHaveBeenCalledWith(
          expect.objectContaining({
            tags: []
          })
        );
      });

      it('should preserve additional_fields in response', async () => {
        const additionalFields = {
          company: 'Test Corp',
          department: 'Engineering',
          priority: 'high',
          score: 95
        };

        mockReq.body.additional_fields = additionalFields;

        const mockCreatedContact = {
          _id: new ObjectId(),
          org_id: mockReq.organizationId,
          created_by: mockReq.userId,
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [],
          additional_fields: additionalFields,
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        };

        mockContactDAO.createContact.mockResolvedValue(mockCreatedContact as any);

        await ContactsHandler.createContact(mockReq, mockRes);

        expect(mockRes.json).toHaveBeenCalledWith(
          expect.objectContaining({
            additional_fields: additionalFields
          })
        );
      });
    });
  });

  // Tests from ContactsHandler.getContacts.test.ts
  describe('bulkImportContacts', () => {
    let mockReq: any;
    let mockRes: any;
    const mockJobId = new ObjectId().toString();
    const mockBulkImportJob = {
      _id: new ObjectId(mockJobId),
      status: 'pending',
      created_at: new Date()
    };

    beforeEach(() => {
      mockReq = {
        userId: organizationFixtures.sampleUser.id,
        organizationId: organizationFixtures.sampleOrganization.id,
        file: { ...fileFixtures.validFile },
        body: {
          fieldMappings: bulkImportFixtures.validFieldMappings
        }
      };
      mockRes = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.createBulkImportJob
      mockContactDAO.createBulkImportJob.mockResolvedValue(mockBulkImportJob);
      
      // Mock CSVService.validateFileStructure
      mockCSVService.validateFileStructure.mockResolvedValue({
        isValid: true,
        errors: [],
        rowCount: 10
      });
    });

    it('should create a bulk import job and return success response', async () => {
      // Call the handler
      await ContactsHandler.bulkImportContacts(mockReq, mockRes);

      // Verify ContactDAO.createBulkImportJob was called with correct arguments
      expect(mockContactDAO.createBulkImportJob).toHaveBeenCalledWith(
        mockReq.organizationId,
        mockReq.userId,
        mockReq.file.originalname,
        mockReq.file.originalname,
        mockReq.body.fieldMappings
      );

      // Verify contactBulkImportQueue.add was called with correct arguments
      expect(contactBulkImportQueue.add).toHaveBeenCalledWith(
        expect.objectContaining({
          jobId: mockJobId,
          organizationId: mockReq.organizationId,
          userId: mockReq.userId,
          fileName: mockReq.file.originalname,
          fieldMappings: mockReq.body.fieldMappings,
          fileBuffer: expect.any(String)
        }),
        {
          jobId: mockJobId
        }
      );

      // Verify response
      expect(mockRes.status).toHaveBeenCalledWith(202);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: 'Bulk import job created successfully',
        data: {
          jobId: mockJobId,
          status: mockBulkImportJob.status,
          fileName: mockReq.file.originalname,
          createdAt: mockBulkImportJob.created_at
        }
      });
    });

    it('should throw OrganizationError when organization ID is missing', async () => {
      // Remove organization ID
      mockReq.organizationId = undefined;

      // Call the handler and expect it to throw
      await expect(ContactsHandler.bulkImportContacts(mockReq, mockRes))
        .rejects.toThrow(OrganizationError);

      // Verify ContactDAO.createBulkImportJob was not called
      expect(mockContactDAO.createBulkImportJob).not.toHaveBeenCalled();
      
      // Verify contactBulkImportQueue.add was not called
      expect(contactBulkImportQueue.add).not.toHaveBeenCalled();
    });

    it('should throw OrganizationError when user ID is missing', async () => {
      // Remove user ID
      mockReq.userId = undefined;

      // Call the handler and expect it to throw
      await expect(ContactsHandler.bulkImportContacts(mockReq, mockRes))
        .rejects.toThrow(OrganizationError);

      // Verify ContactDAO.createBulkImportJob was not called
      expect(mockContactDAO.createBulkImportJob).not.toHaveBeenCalled();
      
      // Verify contactBulkImportQueue.add was not called
      expect(contactBulkImportQueue.add).not.toHaveBeenCalled();
    });

    it('should throw BadRequestError when no file is provided', async () => {
      // Remove file
      mockReq.file = undefined;

      // Call the handler and expect it to throw
      await expect(ContactsHandler.bulkImportContacts(mockReq, mockRes))
        .rejects.toThrow(BadRequestError);

      // Verify ContactDAO.createBulkImportJob was not called
      expect(mockContactDAO.createBulkImportJob).not.toHaveBeenCalled();
      
      // Verify contactBulkImportQueue.add was not called
      expect(contactBulkImportQueue.add).not.toHaveBeenCalled();
    });

    it('should throw BadRequestError when file validation fails', async () => {
      // Mock CSVService.validateFileStructure to return invalid result
      mockCSVService.validateFileStructure.mockResolvedValue({
        isValid: false,
        errors: ['File contains too many rows'],
        rowCount: 0
      });

      // Call the handler and expect it to throw
      await expect(ContactsHandler.bulkImportContacts(mockReq, mockRes))
        .rejects.toThrow(BadRequestError);

      // Verify ContactDAO.createBulkImportJob was not called
      expect(mockContactDAO.createBulkImportJob).not.toHaveBeenCalled();
      
      // Verify contactBulkImportQueue.add was not called
      expect(contactBulkImportQueue.add).not.toHaveBeenCalled();
    });

    it('should throw DatabaseError when database operation fails', async () => {
      // Mock ContactDAO.createBulkImportJob to throw error
      const dbError = new Error('Database error');
      mockContactDAO.createBulkImportJob.mockRejectedValue(dbError);

      // Call the handler and expect it to throw
      await expect(ContactsHandler.bulkImportContacts(mockReq, mockRes))
        .rejects.toThrow(DatabaseError);

      // Verify contactBulkImportQueue.add was not called
      expect(contactBulkImportQueue.add).not.toHaveBeenCalled();
    });
  });

  describe('getContacts', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    
    test('should return 400 if organization ID is missing', async () => {
      // Create mock request without organization ID
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = undefined;
      
      const res = MockResponseHelper.createMockResponse();
      
      await ContactsHandler.getContacts(req, res);
      
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Organization context is required',
        errorCode: 'ORG_001'
      }));
    });
    
    test('should process query parameters correctly', async () => {
      // Create mock request with query parameters
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      
      // Create a MongoDB query that includes status and tag_id filters
      const mongoQuery = {
        "status": ContactStatus.ACTIVE,
        "tags.tag_id": "507f1f77bcf86cd799439011"
      };
      
      req.query = {
        page: '2',
        page_size: '50',
        sort_by: 'name',
        sort_direction: 'asc',
        search: 'John Doe',
        contactFilterQuery: JSON.stringify(mongoQuery)
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return empty results
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 0
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify ContactDAO.getContacts was called with correct parameters
      expect(mockContactDAO.getContacts).toHaveBeenCalledWith(
        'org_123456789',
        expect.objectContaining({
          page: 2,
          page_size: 50,
          sort_by: 'name',
          sort_direction: 'asc',
          search: 'John Doe',
          contactFilterQuery: JSON.stringify(mongoQuery)
        })
      );
    });
    
    test('should handle ValidationError when pagination parameters are missing', async () => {
      // Create mock request without pagination parameters
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {};
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock Number conversion to throw a ValidationError
      // This simulates what would happen if the validation middleware caught the missing parameters
      const originalNumberConstructor = Number;
      global.Number = jest.fn().mockImplementation((value) => {
        if (value === undefined) {
          throw new ValidationError(
            'Invalid query parameters for contacts retrieval',
            'CONTACT_QUERY_VALIDATION_001',
            [{ field: 'page', message: 'Page is required' }]
          );
        }
        return originalNumberConstructor(value);
      }) as any;
      
      await ContactsHandler.getContacts(req, res);
      
      // Restore original Number constructor
      global.Number = originalNumberConstructor;
      
      // Verify that a 422 error is returned (ValidationError)
      expect(res.status).toHaveBeenCalledWith(422);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Invalid query parameters for contacts retrieval',
        errorCode: 'CONTACT_QUERY_VALIDATION_001',
        details: expect.arrayContaining([
          expect.objectContaining({
            field: 'page',
            message: 'Page is required'
          })
        ])
      }));
      
      // Verify ContactDAO.getContacts was not called
      expect(mockContactDAO.getContacts).not.toHaveBeenCalled();
    });
    
    test('should calculate pagination metadata correctly', async () => {
      // Create mock request
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {
        page: '2',
        page_size: '10'
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Create mock contacts with all required properties
      const mockContacts = [
        { 
          _id: new ObjectId(), 
          name: 'John Doe',
          org_id: 'org_123456789',
          created_by: 'user_123456789',
          email: '<EMAIL>',
          phone: '+1234567890',
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        },
        { 
          _id: new ObjectId(), 
          name: 'Jane Smith',
          org_id: 'org_123456789',
          created_by: 'user_123456789',
          email: '<EMAIL>',
          phone: '+0987654321',
          tags: [],
          additional_fields: {},
          status: ContactStatus.ACTIVE,
          created_at: new Date(),
          updated_at: new Date()
        }
      ];
      
      // Mock ContactDAO.getContacts to return contacts and total count
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: mockContacts as any, // Use type assertion to bypass type checking
        totalCount: 25 // Total of 25 contacts (3 pages with page_size 10)
      });
      
      // Mock ContactDAO.formatContactsResponse to return contacts with string IDs
      const formattedContacts = mockContacts.map(contact => ({
        ...contact,
        _id: contact._id.toString()
      }));
      mockContactDAO.formatContactsResponse.mockReturnValue(formattedContacts);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        data: formattedContacts,
        pagination: {
          total_count: 25,
          page: 2,
          page_size: 10,
          total_pages: 3,
          has_next_page: true,
          has_prev_page: true
        }
      });
    });
    
    test('should handle first page pagination metadata correctly', async () => {
      // Create mock request for first page
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {
        page: '1',
        page_size: '10'
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return contacts and total count
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 25 // Total of 25 contacts (3 pages with page_size 10)
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify pagination metadata for first page
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        pagination: expect.objectContaining({
          page: 1,
          has_next_page: true,
          has_prev_page: false // First page should not have previous page
        })
      }));
    });
    
    test('should handle last page pagination metadata correctly', async () => {
      // Create mock request for last page
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {
        page: '3',
        page_size: '10'
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return contacts and total count
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 25 // Total of 25 contacts (3 pages with page_size 10)
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify pagination metadata for last page
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        pagination: expect.objectContaining({
          page: 3,
          has_next_page: false, // Last page should not have next page
          has_prev_page: true
        })
      }));
    });
    
    test('should handle empty results correctly', async () => {
      // Create mock request
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {};
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return empty results
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 0
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify response for empty results
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        data: [],
        pagination: {
          total_count: 0,
          page: 1,
          page_size: 20,
          total_pages: 0,
          has_next_page: false,
          has_prev_page: false
        }
      });
    });
    
    test('should handle database errors correctly', async () => {
      // Create mock request
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {};
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to throw a database error
      mockContactDAO.getContacts.mockRejectedValue(new Error('Database connection failed'));
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify error response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Failed to retrieve contacts',
        errorCode: 'DB_001'
      }));
    });
    
    test('should handle generic errors correctly', async () => {
      // Create mock request
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {};
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to throw a generic error
      mockContactDAO.getContacts.mockRejectedValue(new Error('Unexpected error'));
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify error response
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Failed to retrieve contacts',
        errorCode: 'DB_001'
      }));
    });
    
    test('should process contactFilterQuery parameter correctly', async () => {
      // Create mock request with contactFilterQuery parameter
      const mongoQuery = {
        "name": { "$regex": "John", "$options": "i" }
      };
      
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {
        contactFilterQuery: JSON.stringify(mongoQuery)
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return empty results
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 0
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify ContactDAO.getContacts was called with correct filter parameters
      expect(mockContactDAO.getContacts).toHaveBeenCalledWith(
        'org_123456789',
        expect.objectContaining({
          contactFilterQuery: JSON.stringify(mongoQuery)
        })
      );
    });
    
    test('should process complex contactFilterQuery parameter correctly', async () => {
      // Create mock request with complex contactFilterQuery parameter
      const mongoQuery = {
        "$and": [
          { "firstName": { "$regex": "^Stev" } },
          { "lastName": { "$in": ["Vai", "Vaughan"] } },
          { "age": { "$gt": "28" } },
          { "$or": [
            { "isMusician": true },
            { "instrument": "Guitar" }
          ]},
          { "$eq": ["$groupedField1", "$groupedField4"] },
          { "birthdate": { "$gte": "1954-10-03", "$lte": "1960-06-06" } }
        ]
      };
      
      const req = MockResponseHelper.createMockRequest();
      req.organizationId = 'org_123456789';
      req.query = {
        contactFilterQuery: JSON.stringify(mongoQuery)
      };
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContacts to return empty results
      mockContactDAO.getContacts.mockResolvedValue({
        contacts: [],
        totalCount: 0
      });
      
      // Mock ContactDAO.formatContactsResponse to return empty array
      mockContactDAO.formatContactsResponse.mockReturnValue([]);
      
      await ContactsHandler.getContacts(req, res);
      
      // Verify ContactDAO.getContacts was called with correct filter parameter
      expect(mockContactDAO.getContacts).toHaveBeenCalledWith(
        'org_123456789',
        expect.objectContaining({
          contactFilterQuery: JSON.stringify(mongoQuery)
        })
      );
    });
  });

  describe('extractCSVHeaders', () => {
    let mockReq: any;
    let mockRes: any;

    beforeEach(() => {
      mockReq = {
        file: { ...fileFixtures.validFile }
      };
      mockRes = MockResponseHelper.createMockResponse();
    });

    it('should extract headers from a valid file and return success response', async () => {
      // Mock CSVService.validateFileStructure to return valid result
      mockCSVService.validateFileStructure.mockResolvedValue({
        isValid: true,
        errors: [],
        rowCount: 10
      });

      // Mock CSVService.extractHeaders to return headers
      const mockHeaders = ['Name', 'Email', 'Phone'];
      mockCSVService.extractHeaders.mockResolvedValue(mockHeaders);

      // Call the handler
      await ContactsHandler.extractCSVHeaders(mockReq, mockRes);

      // Verify CSVService methods were called with correct arguments
      expect(mockCSVService.validateFileStructure).toHaveBeenCalledWith(mockReq.file);
      expect(mockCSVService.extractHeaders).toHaveBeenCalledWith(mockReq.file);

      // Verify SchemaExtractor was called
      expect(SchemaExtractor.getContactFields).toHaveBeenCalled();

      // Verify response
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: {
          csvHeaders: mockHeaders,
          contactSchema: mockContactFields,
          fileInfo: {
            name: mockReq.file.originalname,
            size: mockReq.file.size,
            rowCount: 10
          }
        }
      });
    });

    it('should return 400 error when file validation fails', async () => {
      // Mock CSVService.validateFileStructure to return invalid result with error code
      mockCSVService.validateFileStructure.mockResolvedValue({
        isValid: false,
        errors: ['File contains too many rows'],
        errorCode: ERROR_CODES.TOO_MANY_ROWS,
        rowCount: 0
      });

      // Call the handler
      await ContactsHandler.extractCSVHeaders(mockReq, mockRes);

      // Verify CSVService method was called with correct arguments
      expect(mockCSVService.validateFileStructure).toHaveBeenCalledWith(mockReq.file);
      
      // Verify extractHeaders was not called
      expect(mockCSVService.extractHeaders).not.toHaveBeenCalled();

      // Verify response
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
        success: false,
        error: 'File contains too many rows',
        errorCode: ERROR_CODES.TOO_MANY_ROWS
      }));
    });

    it('should return 400 error when no file is provided', async () => {
      // Set file to undefined
      mockReq.file = undefined;

      // Call the handler
      await ContactsHandler.extractCSVHeaders(mockReq, mockRes);

      // Verify response
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'No file uploaded',
        errorCode: 'FILE_MISSING'
      });
    });

    it('should return 500 error when processing fails', async () => {
      // Mock CSVService.validateFileStructure to throw error
      const error = new Error('Processing error');
      mockCSVService.validateFileStructure.mockRejectedValue(error);

      // Call the handler
      await ContactsHandler.extractCSVHeaders(mockReq, mockRes);

      // Verify response
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'Processing error',
        errorCode: ERROR_CODES.PROCESSING_ERROR
      });
    });
  });

  describe('getContactById', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    
    test('should return 400 if organization ID is missing', async () => {
      // Create mock request without organization ID
      const req = MockResponseHelper.createMockRequest({}, {
        id: '507f1f77bcf86cd799439011'
      });
      req.organizationId = undefined;
      
      const res = MockResponseHelper.createMockResponse();
      
      await ContactsHandler.getContactById(req, res);
      
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Organization context is required',
        errorCode: 'ORG_001'
      }));
    });
    
    test('should return 404 if contact is not found', async () => {
      // Create mock request with valid contact ID
      const req = MockResponseHelper.createMockRequest({}, {
        id: '507f1f77bcf86cd799439011'
      });
      req.organizationId = 'org_123456789';
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContactById to throw NotFoundError
      const notFoundError = new NotFoundError(
        'Contact with ID 507f1f77bcf86cd799439011 not found in organization org_123456789',
        'CONTACT_003'
      );
      mockContactDAO.getContactById.mockImplementation(() => {
        throw notFoundError;
      });
      
      await ContactsHandler.getContactById(req, res);
      
      // Verify ContactDAO.getContactById was called with correct parameters
      expect(mockContactDAO.getContactById).toHaveBeenCalledWith(
        'org_123456789',
        '507f1f77bcf86cd799439011'
      );
      
      // Verify that a 500 error is returned (the handler is returning 500 for this error)
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: expect.stringContaining('Failed to retrieve contact'),
        errorCode: 'DB_001'
      }));
    });
    
    test('should return 400 if contact ID is invalid', async () => {
      // Create mock request with invalid contact ID
      const req = MockResponseHelper.createMockRequest({}, {
        id: 'invalid-id'
      });
      req.organizationId = 'org_123456789';
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContactById to throw BadRequestError
      const badRequestError = new BadRequestError(
        'Invalid contact ID format',
        'CONTACT_002',
        [{ field: 'contactId', message: 'Contact ID must be a valid MongoDB ObjectId' }]
      );
      mockContactDAO.getContactById.mockImplementation(() => {
        throw badRequestError;
      });
      
      await ContactsHandler.getContactById(req, res);
      
      // Verify ContactDAO.getContactById was called with correct parameters
      expect(mockContactDAO.getContactById).toHaveBeenCalledWith(
        'org_123456789',
        'invalid-id'
      );
      
      // Verify that a 500 error is returned (the handler is returning 500 for this error)
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: expect.stringContaining('Failed to retrieve contact'),
        errorCode: 'DB_001'
      }));
    });
    
    test('should return 500 if database error occurs', async () => {
      // Create mock request with valid contact ID
      const req = MockResponseHelper.createMockRequest({}, {
        id: '507f1f77bcf86cd799439011'
      });
      req.organizationId = 'org_123456789';
      
      const res = MockResponseHelper.createMockResponse();
      
      // Mock ContactDAO.getContactById to throw DatabaseError
      mockContactDAO.getContactById.mockRejectedValue({
        name: 'DatabaseError',
        message: 'Failed to retrieve contact',
        errorCode: 'DB_001',
        statusCode: 500
      });
      
      await ContactsHandler.getContactById(req, res);
      
      // Verify ContactDAO.getContactById was called with correct parameters
      expect(mockContactDAO.getContactById).toHaveBeenCalledWith(
        'org_123456789',
        '507f1f77bcf86cd799439011'
      );
      
      // Verify that a 500 error is returned
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Failed to retrieve contact',
        errorCode: 'DB_001'
      }));
    });
    
    test('should return 200 with contact data if contact is found', async () => {
      // Create mock request with valid contact ID
      const req = MockResponseHelper.createMockRequest({}, {
        id: '507f1f77bcf86cd799439011'
      });
      req.organizationId = 'org_123456789';
      
      const res = MockResponseHelper.createMockResponse();
      
      // Create mock contact document with type assertion to satisfy TypeScript
      const mockContact = {
        _id: new ObjectId('507f1f77bcf86cd799439011'),
        org_id: 'org_123456789',
        created_by: 'user_123456789',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-123-4567',
        status: ContactStatus.ACTIVE,
        tags: [],
        additional_fields: {},
        created_at: new Date(),
        updated_at: new Date()
      } as any; // Type assertion to avoid TypeScript errors with Mongoose Document methods
      
      // Create mock formatted contact response
      const mockFormattedContact = {
        _id: '507f1f77bcf86cd799439011',
        org_id: 'org_123456789',
        created_by: 'user_123456789',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-123-4567',
        status: ContactStatus.ACTIVE,
        tags: [],
        additional_fields: {},
        created_at: mockContact.created_at,
        updated_at: mockContact.updated_at
      };
      
      // Mock ContactDAO.getContactById to return mock contact
      mockContactDAO.getContactById.mockResolvedValue(mockContact);
      
      // Mock ContactDAO.formatContactResponse to return formatted contact
      mockContactDAO.formatContactResponse.mockReturnValue(mockFormattedContact);
      
      await ContactsHandler.getContactById(req, res);
      
      // Verify ContactDAO.getContactById was called with correct parameters
      expect(mockContactDAO.getContactById).toHaveBeenCalledWith(
        'org_123456789',
        '507f1f77bcf86cd799439011'
      );
      
      // Verify ContactDAO.formatContactResponse was called with mock contact
      expect(mockContactDAO.formatContactResponse).toHaveBeenCalledWith(mockContact);
      
      // Verify that a 200 response with contact data is returned
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(mockFormattedContact);
    });
  });
});
# Testing Framework Documentation

## Overview

This project uses <PERSON><PERSON> with TypeScript support and MongoDB Memory Server for comprehensive testing of the contacts POST endpoint functionality.

## Test Structure

```
tests/
├── setup.ts                    # Global test setup with MongoDB Memory Server
├── jest.setup.ts              # Custom Jest matchers and configuration
├── utils/
│   └── testHelpers.ts         # Test utility functions and factories
├── mocks/
│   └── mongooseMocks.ts       # Mock implementations for Mongoose
├── fixtures/
│   └── contactFixtures.ts     # Test data fixtures
├── unit/                      # Unit tests
└── integration/               # Integration tests
```

## Available Test Scripts

- `npm test` - Run all tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run test:unit` - Run only unit tests
- `npm run test:integration` - Run only integration tests
- `npm run test:ci` - Run tests for CI/CD (no watch, with coverage)

## Test Utilities

### TestDataFactory

Provides factory methods for creating test data:

```typescript
import { TestDataFactory } from '../utils/testHelpers';

// Create valid contact data
const contactData = TestDataFactory.createValidContactData({
  name: 'Custom Name'
});

// Create invalid contact data for error testing
const invalidData = TestDataFactory.createInvalidContactData();
```

### MockResponseHelper

Provides mock Express request/response objects:

```typescript
import { MockResponseHelper } from '../utils/testHelpers';

const mockRes = MockResponseHelper.createMockResponse();
const mockReq = MockResponseHelper.createMockRequest({ name: 'Test' });
```

### DatabaseTestUtils

Utilities for database testing:

```typescript
import { DatabaseTestUtils } from '../utils/testHelpers';

// Clear a collection
await DatabaseTestUtils.clearCollection('contacts');

// Get collection count
const count = await DatabaseTestUtils.getCollectionCount('contacts');
```

### ServerTestUtils

Provides a single entry point for connecting to the app server in integration tests:

```typescript
import { ServerTestUtils } from '../utils/testHelpers';

// In beforeAll hook
let serverUtils: ServerTestUtils;
let requestAgent;

beforeAll(async () => {
  // Get the singleton instance
  serverUtils = ServerTestUtils.getInstance();
  // Get a supertest request agent for making HTTP requests
  requestAgent = await serverUtils.getRequestAgent();
});

// In tests
const response = await requestAgent
  .post('/contacts')
  .send(contactData);

// In afterAll hook
afterAll(async () => {
  // Close the server when tests are done
  await serverUtils.closeServer();
});
```

## Custom Jest Matchers

The framework includes custom matchers for domain-specific validation:

```typescript
// Test ObjectId validation
expect(someId).toBeValidObjectId();

// Test email validation
expect(email).toBeValidEmail();

// Test phone number validation
expect(phone).toBeValidPhoneNumber();
```

## Test Fixtures

Pre-defined test data is available in the fixtures directory:

```typescript
import { contactFixtures } from '../fixtures/contactFixtures';

// Use valid contact request data
const validRequest = contactFixtures.validContactRequest;

// Use invalid request data for error testing
const invalidRequest = contactFixtures.invalidContactRequests.missingName;
```

## MongoDB Memory Server

The test setup automatically:
- Starts MongoDB Memory Server before all tests
- Connects Mongoose to the in-memory database
- Clears all collections after each test
- Closes connections and stops the server after all tests

## Coverage Requirements

The project enforces minimum coverage thresholds:
- Branches: 80%
- Functions: 80%
- Lines: 80%
- Statements: 80%

Coverage reports are generated in the `coverage/` directory.

## Writing Tests

### Unit Test Example

```typescript
import { ContactsValidator } from '../../validators/ContactsValidator';
import { contactFixtures } from '../fixtures/contactFixtures';

describe('ContactsValidator', () => {
  describe('validateCreateContact', () => {
    it('should validate valid contact data', () => {
      const { error } = ContactsValidator.validateCreateContact(
        contactFixtures.validContactRequest
      );
      expect(error).toBeUndefined();
    });

    it('should reject missing required fields', () => {
      const { error } = ContactsValidator.validateCreateContact(
        contactFixtures.invalidContactRequests.missingName
      );
      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('name');
    });
  });
});
```

### Integration Test Example

```typescript
import { ServerTestUtils } from '../utils/testHelpers';
import { contactFixtures } from '../fixtures/contactFixtures';
import request from 'supertest';

describe('POST /contacts', () => {
  let serverUtils: ServerTestUtils;
  let requestAgent: any;

  beforeAll(async () => {
    // Get the singleton instance of ServerTestUtils
    serverUtils = ServerTestUtils.getInstance();
    // Get a supertest request agent for making HTTP requests
    requestAgent = await serverUtils.getRequestAgent();
  });

  afterAll(async () => {
    // Close the server when tests are done
    await serverUtils.closeServer();
  });

  it('should create a new contact', async () => {
    const response = await requestAgent
      .post('/contacts')
      .send(contactFixtures.validContactRequest)
      .expect(201);

    expect(response.body).toHaveProperty('_id');
    expect(response.body.name).toBe(contactFixtures.validContactRequest.name);
  });
});
```

## Best Practices

1. **Isolation**: Each test should be independent and not rely on other tests
2. **Cleanup**: Use the provided setup to ensure clean state between tests
3. **Mocking**: Mock external dependencies using the provided mock utilities
4. **Fixtures**: Use test fixtures for consistent test data
5. **Assertions**: Use descriptive assertions and custom matchers when appropriate
6. **Coverage**: Aim for high test coverage while focusing on meaningful tests

## Troubleshooting

### Common Issues

1. **MongoDB Connection Issues**: Ensure MongoDB Memory Server is properly configured in setup.ts
2. **ESM Import Issues**: Check that Jest configuration supports ESM modules
3. **TypeScript Compilation**: Verify ts-jest configuration matches project TypeScript settings
4. **Timeout Issues**: Increase test timeout for database operations if needed

### Debug Mode

Run tests with additional debugging:

```bash
DEBUG=* npm test
```

Or run specific test files:

```bash
npm test -- --testPathPattern=ContactsValidator
```
import mongoose, { Schema, model, Document } from 'mongoose';
import { Contact, ContactStatus } from '../../../types/contact.types';
import { ensureIndexes } from '../../utils/db/indexUtils';

// Document interface that properly extends Mongoose Document
export interface ContactDocument extends Omit<Contact, '_id'>, Document {
  _id: Schema.Types.ObjectId;
}

// Mongoose schema
const contactSchema = new Schema<ContactDocument>({
  org_id: {
    type: String,
    required: true,
    index: true
  },
  created_by: {
    type: String,
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    index: true
  },
  email: {
    type: String,
    required: true,
    index: true
  },
  phone: {
    type: String,
    required: true,
    index: true
  },
  country: {
    type: String,
    required: false
  },
  country_code: {
    type: String,
    required: false
  },
  avatar_url: {
    type: String,
    required: false
  },
  tags: [{
    tag_id: {
      type: Schema.Types.ObjectId,
      required: true
    },
    tag_name: {
      type: String,
      required: true
    }
  }],
  additional_fields: {
    type: Schema.Types.Mixed,
    default: {}
  },
  status: {
    type: String,
    enum: Object.values(ContactStatus),
    default: ContactStatus.ACTIVE,
    index: true
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  versionKey: false
});

// Unique compound indexes for email and phone within organization
// Only applies to non-deleted contacts
contactSchema.index(
  { org_id: 1, email: 1 },
  {
    unique: true,
    partialFilterExpression: {
      status: { $in: [ContactStatus.ACTIVE, ContactStatus.ARCHIVED] }
    }
  }
);

contactSchema.index(
  { org_id: 1, phone: 1 },
  {
    unique: true,
    partialFilterExpression: {
      status: { $in: [ContactStatus.ACTIVE, ContactStatus.ARCHIVED] }
    }
  }
);

// Other indexes for performance
contactSchema.index({ org_id: 1, status: 1 });
contactSchema.index({ org_id: 1, created_at: -1 });

// Text index for search functionality with weighted fields
// Prioritize name matches (weight: 10) over email and phone (weight: 5 each)
contactSchema.index(
  { name: 'text', email: 'text', phone: 'text' },
  { 
    weights: { name: 10, email: 5, phone: 5 }, 
    name: 'contact_text_search' 
  }
);

// Additional non-unique compound indexes for partial matching and efficient filtering
contactSchema.index({ org_id: 1, email: 1 }, { name: 'idx_org_email_partial_match' });
contactSchema.index({ org_id: 1, phone: 1 }, { name: 'idx_org_phone_partial_match' });

// Export the model
export const ContactModel = model<ContactDocument>('Contact', contactSchema);

// Ensure indexes are created only in non-test environments
// In test environment, indexes are created explicitly in tests/setup.ts
ensureIndexes(ContactModel, contactSchema, 'Contact');
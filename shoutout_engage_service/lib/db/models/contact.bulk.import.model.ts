import { Schema, model, Document } from 'mongoose';
import { BULK_IMPORT_STATUS, BulkImportError, FieldMapping } from '../../../types/contact.types';
import { ensureIndexes } from '../../utils/db/indexUtils';
import {ObjectId} from "mongodb";

/**
 * Interface for contact bulk import document
 */
export interface ContactBulkImportDocument extends Document {
    _id: ObjectId;
    org_id: string;
    created_by: string;
    file_id: string;
    file_name: string;
    field_mappings: FieldMapping[];
    status: BULK_IMPORT_STATUS;
    total_rows: number;
    processed_rows: number;
    successful_rows: number;
    failed_rows: number;
    errors: BulkImportError[];
    started_at?: Date;
    completed_at?: Date;
    created_at: Date;
    updated_at: Date;
}

/**
 * Schema for contact bulk import
 */
const contactBulkImportSchema = new Schema<ContactBulkImportDocument>({
    org_id: {
        type: String,
        required: true,
        index: true
    },
    created_by: {
        type: String,
        required: true,
        index: true
    },
    file_id: {
        type: String,
        required: true
    },
    file_name: {
        type: String,
        required: true
    },
    field_mappings: [{
        fileColumnName: {
            type: String,
            required: true
        },
        systemAttributeName: {
            type: String,
            required: true
        }
    }],
    status: {
        type: String,
        enum: Object.values(BULK_IMPORT_STATUS),
        default: BULK_IMPORT_STATUS.PENDING,
        index: true
    },
    total_rows: {
        type: Number,
        default: 0
    },
    processed_rows: {
        type: Number,
        default: 0
    },
    successful_rows: {
        type: Number,
        default: 0
    },
    failed_rows: {
        type: Number,
        default: 0
    },
    errors: [{
        row: {
            type: Number,
            required: true
        },
        type: {
            type: String,
            required: true
        },
        message: {
            type: String,
            required: true
        },
        field: {
            type: String
        },
        value: {
            type: String
        }
    }],
    started_at: {
        type: Date
    },
    completed_at: {
        type: Date
    }
}, {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
    versionKey: false
});

// Indexes for performance
contactBulkImportSchema.index({ org_id: 1, status: 1 });
contactBulkImportSchema.index({ org_id: 1, created_at: -1 });
contactBulkImportSchema.index({ created_by: 1, created_at: -1 });

// Export the model
export const ContactBulkImportModel = model<ContactBulkImportDocument>('ContactBulkImport', contactBulkImportSchema);

// Ensure indexes are created only in non-test environments
ensureIndexes(ContactBulkImportModel, contactBulkImportSchema, 'ContactBulkImport');
import mongoose, { Schema, Document } from 'mongoose';
import { ContactActivity, ContactEventType } from '../../../types/activity.types';
import { ObjectId } from 'mongodb';

// Define the document interface
export interface ContactActivityDocument extends Omit<ContactActivity, '_id'>, Document {}

/**
 * Contact Activity schema for tracking contact interactions and events
 */
const ContactActivitySchema = new Schema<ContactActivityDocument>({
  org_id: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  contact_id: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Contact',
    index: true,
    validate: {
      validator: function(v: any) {
        return ObjectId.isValid(v);
      },
      message: 'Invalid contact_id format'
    }
  },
  event_type: {
    type: String,
    required: true,
    enum: {
      values: Object.values(ContactEventType),
      message: 'Invalid event type. Must be one of: {VALUES}'
    },
    index: true
  },
  campaign_id: {
    type: String,
    trim: true,
    sparse: true, // Allow null/undefined values but create index only for non-null values
    index: true
  },
  campaign_name: {
    type: String,
    trim: true,
    maxlength: [255, 'Campaign name cannot exceed 255 characters']
  },
  metadata: {
    type: Schema.Types.Mixed,
    default: {},
    validate: {
      validator: function(v: any) {
        // Ensure metadata is an object if provided
        return v === null || v === undefined || typeof v === 'object';
      },
      message: 'Metadata must be an object'
    }
  },
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: false }, // Only track creation time
  collection: 'contact_activities'
});

// Compound indexes for efficient querying
ContactActivitySchema.index({ org_id: 1, contact_id: 1 });
ContactActivitySchema.index({ org_id: 1, event_type: 1 });
ContactActivitySchema.index({ org_id: 1, campaign_id: 1 });
ContactActivitySchema.index({ contact_id: 1, created_at: -1 });
ContactActivitySchema.index({ org_id: 1, created_at: -1 });

// Pre-save hook to ensure created_at is set
ContactActivitySchema.pre('save', function(next) {
  if (!this.created_at) {
    this.created_at = new Date();
  }
  next();
});

// Export the model
export const ContactActivityModel = mongoose.model<ContactActivityDocument>('ContactActivity', ContactActivitySchema);

export default ContactActivitySchema;

import { ContactModel, ContactDocument } from '../models/contact.model';
import { ContactBulkImportModel, ContactBulkImportDocument } from '../models/contact.bulk.import.model';
import {
  ContactStatus,
  ContactResponse,
  CreateContactData,
  ContactsQueryParams
} from '../../../types/contact.types';
import { logger } from '../../logger';
import config from '../../config';
import { escapeRegExp } from '../../utils/stringUtils';
import { 
  DuplicateError, 
  DatabaseError, 
  BadRequestError,
  NotFoundError
} from '../../errors/error-types';
import { ObjectId } from 'mongodb';
import { BULK_IMPORT_STATUS, BulkImportError, FieldMapping } from '../../constant/queue.constants';

const log = logger(config.logger);

export class ContactDAO {
  /**
   * Normalize contact data for database storage
   * @param contactData - Raw contact data
   * @returns Normalized contact data ready for database
   */
  private static normalizeContactData(contactData: CreateContactData) {
    return {
      org_id: contactData.org_id,
      created_by: contactData.created_by,
      name: contactData.name.trim(),
      email: contactData.email.toLowerCase().trim(),
      phone: contactData.phone.trim(),
      country: contactData.country?.trim(),
      country_code: contactData.country_code?.toUpperCase().trim(),
      avatar_url: contactData.avatar_url?.trim(),
      tags: contactData.tags || [],
      additional_fields: contactData.additional_fields || {},
      status: contactData.status || ContactStatus.ACTIVE,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * Format contact document for API response
   * @param contact - Contact document from database
   * @returns Formatted contact response
   */
  public static formatContactResponse(contact: ContactDocument): ContactResponse {
    return {
      _id: contact._id.toString(),
      org_id: contact.org_id,
      created_by: contact.created_by,
      name: contact.name,
      email: contact.email,
      phone: contact.phone,
      country: contact.country,
      country_code: contact.country_code,
      avatar_url: contact.avatar_url,
      tags: contact.tags.map((tag: any) => ({
        tag_id: tag.tag_id.toString(),
        tag_name: tag.tag_name
      })),
      additional_fields: contact.additional_fields,
      status: contact.status,
      created_at: contact.created_at,
      updated_at: contact.updated_at
    };
  }

  /**
   * Format multiple contacts for API response
   * @param contacts - Array of contact documents
   * @returns Array of formatted contact responses
   */
  static formatContactsResponse(contacts: ContactDocument[]): ContactResponse[] {
    return contacts.map(contact => this.formatContactResponse(contact));
  }

  /**
   * Create a new contact in the database
   * @param contactData - The contact data to create
   * @returns Promise<ContactDocument> - The created contact document
   * @throws DuplicateError if duplicate contact exists
   * @throws DatabaseError if database operation fails
   */
  static async createContact(contactData: CreateContactData): Promise<ContactDocument> {
    try {
      log.info(`Creating contact for organization: ${contactData.org_id}, email: ${contactData.email}`);

      // Normalize and create contact document
      const normalizedData = this.normalizeContactData(contactData);
      const contactDoc = new ContactModel(normalizedData);

      // Save the contact to database - unique indexes will prevent duplicates
      const savedContact = await contactDoc.save();

      log.info(`Contact created successfully with ID: ${savedContact._id} for org: ${contactData.org_id}`);
      return savedContact;

    } catch (error: any) {
      log.error('Error creating contact:', error);

      // Handle MongoDB duplicate key errors
      if (error.code === 11000) {
        const duplicateField = this.getDuplicateField(error.message);
        throw new DuplicateError(
          `Contact with ${duplicateField} already exists in organization`,
          'CONTACT_001'
        );
      }

      // Re-throw other errors as DatabaseError with original error message
      throw new DatabaseError(
        error.message || 'Failed to create contact due to database error',
        'DB_002',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Extract which field caused the duplicate key error
   * @param errorMessage - MongoDB error message
   * @returns string - Human readable duplicate field description
   */
  private static getDuplicateField(errorMessage: string): string {
    if (errorMessage.includes('email')) {
      return 'email';
    } else if (errorMessage.includes('phone')) {
      return 'phone number';
    }
    return 'email or phone number';
  }



  /**
   * Retrieve contacts with pagination, filtering, sorting, and search capabilities
   * @param organizationId - Organization ID to filter contacts by
   * @param params - Query parameters for pagination, filtering, sorting, and search
   * @returns Promise with contacts and total count
   * @throws DatabaseError if database operation fails
   */
  static async getContacts(
    organizationId: string,
    params: ContactsQueryParams): Promise<{ contacts: ContactDocument[], totalCount: number }> {
    try {
      log.info(`Retrieving contacts for organization: ${organizationId} with params: ${JSON.stringify(params)}`);

      // Build query based on parameters
      const query = this.buildContactsQuery(organizationId, params);

      // Calculate pagination
      const page = params.page || 1;
      const pageSize = params.page_size || 20;
      const skip = (page - 1) * pageSize;
      // Build sort options
      const sortField = params.sort_by || 'created_at';
      const sortDirection = params.sort_direction === 'asc' ? 1 : -1;
      const sortOptions: Record<string, any> = { [sortField]: sortDirection };

     // Add secondary sort by created_at if not already sorting by it
      if (sortField !== 'created_at') {
        sortOptions.created_at = -1;
      }

      // Check if we have text search to add text score sorting
      const hasTextSearch = params.search && query.$text;
      let projection = {};

      if (hasTextSearch) {
        // Add text score for relevance sorting
        projection = { score: { $meta: "textScore" } };
        // Prioritize text score for search results
        sortOptions.score = { $meta: "textScore" };
      }

      // Execute single query with conditional projection
      const contacts = await ContactModel.find(query, projection)
          .sort(sortOptions as any)
          .skip(skip)
          .limit(pageSize)
          .exec();

      // Get total count for pagination metadata
      const totalCount = await ContactModel.countDocuments(query);

      log.info(`Retrieved ${contacts.length} contacts out of ${totalCount} total for organization: ${organizationId}`);
      return { contacts, totalCount };
    } catch (error: any) {
      log.error(`Error retrieving contacts for organization: ${organizationId}`, error);
      
      // Throw a DatabaseError with appropriate details
      throw new DatabaseError(
        `Failed to retrieve contacts for organization: ${organizationId}`,
        'DB_003',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Build MongoDB query based on query parameters
   * @param organizationId - Organization ID to filter contacts by
   * @param params - Query parameters for filtering and search
   * @returns MongoDB query object
   * @throws BadRequestError if contactFilterQuery is invalid
   */
  static buildContactsQuery(organizationId: string, params: ContactsQueryParams): Record<string, any> {
    // Start with base conditions that must always be applied
    const baseConditions: Record<string, any>[] = [
      { org_id: organizationId }
    ];

    // Add search condition if provided
    if (params.search) {
      const searchTerm = params.search.trim();
      if (searchTerm) {
        const escapedSearchTerm = escapeRegExp(searchTerm);
        const searchRegex = new RegExp(escapedSearchTerm, 'i');

        baseConditions.push({
          $or: [
            { $text: { $search: searchTerm } }, // Text index search for name (weighted)
            { email: searchRegex },             // Partial match for email
            { phone: searchRegex }              // Partial match for phone
          ]
        });
      }
    }

    // Add filter query condition if provided
    if (params.contactFilterQuery) {
      try {
        const mongoQuery = JSON.parse(params.contactFilterQuery);
        if (mongoQuery && Object.keys(mongoQuery).length > 0) {
          baseConditions.push(mongoQuery);
        }
      } catch (error: any) {
        log.error('Error processing contactFilterQuery:', error);
        
        // Throw a BadRequestError with appropriate details
        throw new BadRequestError(
          'Invalid contactFilterQuery format',
          'QUERY_001',
          [{ field: 'contactFilterQuery', message: 'Must be a valid JSON string representing a MongoDB query' }]
        );
      }
    }

    // If we only have the base org_id condition, return it directly
    if (baseConditions.length === 1) {
      return baseConditions[0];
    }

    // Otherwise, combine all conditions with $and
    return { $and: baseConditions };
  }

  /**
   * Retrieve a contact by ID for a specific organization
   * @param organizationId - Organization ID to filter contacts by
   * @param contactId - Contact ID to retrieve
   * @returns Promise with the contact document
   * @throws NotFoundError if contact is not found
   * @throws DatabaseError if database operation fails
   */
  static async getContactById(
    organizationId: string,
    contactId: string
  ): Promise<ContactDocument> {
    try {
      log.info(`Retrieving contact with ID: ${contactId} for organization: ${organizationId}`);
      
      // Validate contactId format
      if (!ObjectId.isValid(contactId)) {
        throw new BadRequestError(
          'Invalid contact ID format',
          'CONTACT_002',
          [{ field: 'contactId', message: 'Contact ID must be a valid MongoDB ObjectId' }]
        );
      }

      // Find contact by ID and organization ID
      const contact = await ContactModel.findOne({
        _id: new ObjectId(contactId),
        org_id: organizationId
      });

      // If contact is not found, throw NotFoundError
      if (!contact) {
        throw new NotFoundError(
          `Contact with ID ${contactId} not found in organization ${organizationId}`,
          'CONTACT_003'
        );
      }

      log.info(`Successfully retrieved contact with ID: ${contactId} for organization: ${organizationId}`);
      return contact;
    } catch (error: any) {
      // If error is already an AppError (NotFoundError, BadRequestError), rethrow it
      if (error.name === 'NotFoundError' || error.name === 'BadRequestError') {
        throw error;
      }

      log.error(`Error retrieving contact with ID: ${contactId} for organization: ${organizationId}`, error);
      
      // Throw a DatabaseError with appropriate details
      throw new DatabaseError(
        error.message || `Failed to retrieve contact with ID: ${contactId} for organization: ${organizationId}`,
        'DB_004',
        error.message ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Create a new contact bulk import job
   * @param organizationId Organization ID
   * @param userId User ID
   * @param fileId File ID
   * @param fileName File name
   * @param fieldMappings Field mappings
   * @returns Created contact bulk import job
   * @throws DatabaseError if database operation fails
   */
  static async createBulkImportJob(
    organizationId: string,
    userId: string,
    fileId: string,
    fileName: string,
    fieldMappings: FieldMapping[]
  ): Promise<ContactBulkImportDocument> {
    try {
      log.info('Creating contact bulk import job', {
        organizationId,
        userId,
        fileId,
        fileName
      });

      const bulkImportJob = new ContactBulkImportModel({
        org_id: organizationId,
        created_by: userId,
        file_id: fileId,
        file_name: fileName,
        field_mappings: fieldMappings,
        status: BULK_IMPORT_STATUS.PENDING
      });

      const savedJob = await bulkImportJob.save();
      log.info(`Contact bulk import job created with ID: ${savedJob._id} for org: ${organizationId}`);
      return savedJob;
    } catch (error: unknown) {
      log.error('Error creating contact bulk import job', error);
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to create contact bulk import job',
        'BULK_IMPORT_001',
        error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Get a contact bulk import job by ID
   * @param organizationId Organization ID
   * @param jobId Job ID
   * @returns Contact bulk import job
   * @throws NotFoundError if job is not found
   * @throws BadRequestError if job ID is invalid
   * @throws DatabaseError if database operation fails
   */
  static async getBulkImportJobById(
    organizationId: string,
    jobId: string
  ): Promise<ContactBulkImportDocument> {
    try {
      log.info(`Retrieving contact bulk import job with ID: ${jobId} for organization: ${organizationId}`);
      
      // Validate input
      if (!ObjectId.isValid(jobId)) {
        throw new BadRequestError(
          'Invalid job ID format',
          'BULK_IMPORT_002',
          [{ field: 'jobId', message: 'Job ID must be a valid MongoDB ObjectId' }]
        );
      }

      // Find the job
      const job = await ContactBulkImportModel.findOne({
        _id: new ObjectId(jobId),
        org_id: organizationId
      });

      // Check if job exists
      if (!job) {
        throw new NotFoundError(
          `Contact bulk import job with ID ${jobId} not found in organization ${organizationId}`,
          'BULK_IMPORT_003'
        );
      }

      log.info(`Successfully retrieved contact bulk import job with ID: ${jobId} for organization: ${organizationId}`);
      return job;
    } catch (error: unknown) {
      // Rethrow specific errors
      if (error instanceof Error && 
          (error.name === 'NotFoundError' || error.name === 'BadRequestError')) {
        throw error;
      }

      // Convert unknown errors to DatabaseError
      log.error(`Error retrieving contact bulk import job with ID: ${jobId} for organization: ${organizationId}`, error);
      throw new DatabaseError(
        error instanceof Error ? error.message : `Failed to retrieve contact bulk import job`,
        'BULK_IMPORT_004',
        error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Get contact bulk import jobs for an organization with pagination
   * @param organizationId Organization ID
   * @param page Page number
   * @param pageSize Page size
   * @returns Contact bulk import jobs with pagination
   * @throws BadRequestError if pagination parameters are invalid
   * @throws DatabaseError if database operation fails
   */
  static async getBulkImportJobs(
    organizationId: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<{
    jobs: ContactBulkImportDocument[],
    totalCount: number,
    page: number,
    pageSize: number,
    totalPages: number
  }> {
    try {
      log.info(`Retrieving contact bulk import jobs for organization: ${organizationId}, page: ${page}, pageSize: ${pageSize}`);
      
      // Validate pagination parameters
      if (page < 1) {
        throw new BadRequestError(
          'Invalid page number',
          'BULK_IMPORT_005',
          [{ field: 'page', message: 'Page number must be at least 1' }]
        );
      }

      if (pageSize < 1 || pageSize > 100) {
        throw new BadRequestError(
          'Invalid page size',
          'BULK_IMPORT_006',
          [{ field: 'pageSize', message: 'Page size must be between 1 and 100' }]
        );
      }

      // Calculate skip value for pagination
      const skip = (page - 1) * pageSize;

      // Get total count
      const totalCount = await ContactBulkImportModel.countDocuments({
        org_id: organizationId
      });

      // Calculate total pages
      const totalPages = Math.ceil(totalCount / pageSize);

      // Get jobs with pagination
      const jobs = await ContactBulkImportModel.find({
        org_id: organizationId
      })
        .sort({ created_at: -1 })
        .skip(skip)
        .limit(pageSize);

      log.info(`Retrieved ${jobs.length} contact bulk import jobs out of ${totalCount} total for organization: ${organizationId}`);
      return {
        jobs,
        totalCount,
        page,
        pageSize,
        totalPages
      };
    } catch (error: unknown) {
      // Rethrow BadRequestError
      if (error instanceof Error && error.name === 'BadRequestError') {
        throw error;
      }

      // Convert unknown errors to DatabaseError
      log.error(`Error retrieving contact bulk import jobs for organization: ${organizationId}`, error);
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to retrieve contact bulk import jobs',
        'BULK_IMPORT_007',
        error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Update a contact bulk import job
   * @param update Update object
   * @param jobId Job ID
   * @param organizationId Organization ID
   * @param conditions Additional conditions
   * @returns Updated contact bulk import job
   * @throws BadRequestError if job ID is invalid
   * @throws DatabaseError if database operation fails
   */
  static async updateBulkImportJob(
    update: Partial<ContactBulkImportDocument>,
    jobId: string,
    organizationId: string,
    conditions: Partial<ContactBulkImportDocument> = {}
  ): Promise<ContactBulkImportDocument | null> {
    try {
      log.info(`Updating contact bulk import job with ID: ${jobId} for organization: ${organizationId}`);
      
      // Validate input
      if (!ObjectId.isValid(jobId)) {
        throw new BadRequestError(
          'Invalid job ID format',
          'BULK_IMPORT_008',
          [{ field: 'jobId', message: 'Job ID must be a valid MongoDB ObjectId' }]
        );
      }

      // Build query
      const query = {
        _id: new ObjectId(jobId),
        org_id: organizationId,
        ...conditions
      };

      // Update the job
      const updatedJob = await ContactBulkImportModel.findOneAndUpdate(
        query,
        { $set: update },
        { new: true }
      );

      if (updatedJob) {
        log.info(`Successfully updated contact bulk import job with ID: ${jobId} for organization: ${organizationId}`);
      } else {
        log.warn(`No contact bulk import job found to update with ID: ${jobId} for organization: ${organizationId}`);
      }

      return updatedJob;
    } catch (error: unknown) {
      // Rethrow BadRequestError
      if (error instanceof Error && error.name === 'BadRequestError') {
        throw error;
      }

      // Convert unknown errors to DatabaseError
      log.error(`Error updating contact bulk import job with ID: ${jobId} for organization: ${organizationId}`, error);
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to update contact bulk import job',
        'BULK_IMPORT_009',
        error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Add an error to a contact bulk import job
   * @param jobId Job ID
   * @param organizationId Organization ID
   * @param error Error object
   * @returns Updated contact bulk import job
   * @throws BadRequestError if job ID is invalid
   * @throws DatabaseError if database operation fails
   */
  static async addErrorToBulkImportJob(
    jobId: string,
    organizationId: string,
    error: BulkImportError
  ): Promise<ContactBulkImportDocument | null> {
    try {
      log.info(`Adding error to contact bulk import job with ID: ${jobId} for organization: ${organizationId}`);
      
      // Validate input
      if (!ObjectId.isValid(jobId)) {
        throw new BadRequestError(
          'Invalid job ID format',
          'BULK_IMPORT_010',
          [{ field: 'jobId', message: 'Job ID must be a valid MongoDB ObjectId' }]
        );
      }

      // Update the job
      const updatedJob = await ContactBulkImportModel.findOneAndUpdate(
        {
          _id: new ObjectId(jobId),
          org_id: organizationId
        },
        {
          $push: { errors: error },
          $inc: { failed_rows: 1 }
        },
        { new: true }
      );

      if (updatedJob) {
        log.info(`Successfully added error to contact bulk import job with ID: ${jobId} for organization: ${organizationId}`);
      } else {
        log.warn(`No contact bulk import job found to add error with ID: ${jobId} for organization: ${organizationId}`);
      }

      return updatedJob;
    } catch (error: unknown) {
      // Rethrow BadRequestError
      if (error instanceof Error && error.name === 'BadRequestError') {
        throw error;
      }

      // Convert unknown errors to DatabaseError
      log.error(`Error adding error to contact bulk import job with ID: ${jobId} for organization: ${organizationId}`, error);
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to add error to contact bulk import job',
        'BULK_IMPORT_011',
        error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }

  /**
   * Increment processed and successful rows count
   * @param jobId Job ID
   * @param organizationId Organization ID
   * @param count Count to increment
   * @returns Updated contact bulk import job
   * @throws BadRequestError if job ID is invalid
   * @throws DatabaseError if database operation fails
   */
  static async incrementSuccessfulRows(
    jobId: string,
    organizationId: string,
    count: number = 1
  ): Promise<ContactBulkImportDocument | null> {
    try {
      log.info(`Incrementing successful rows for contact bulk import job with ID: ${jobId} for organization: ${organizationId}`);
      
      // Validate input
      if (!ObjectId.isValid(jobId)) {
        throw new BadRequestError(
          'Invalid job ID format',
          'BULK_IMPORT_012',
          [{ field: 'jobId', message: 'Job ID must be a valid MongoDB ObjectId' }]
        );
      }

      // Update the job
      const updatedJob = await ContactBulkImportModel.findOneAndUpdate(
        {
          _id: new ObjectId(jobId),
          org_id: organizationId
        },
        {
          $inc: {
            processed_rows: count,
            successful_rows: count
          }
        },
        { new: true }
      );

      if (updatedJob) {
        log.info(`Successfully incremented successful rows for contact bulk import job with ID: ${jobId} for organization: ${organizationId}`);
      } else {
        log.warn(`No contact bulk import job found to increment successful rows with ID: ${jobId} for organization: ${organizationId}`);
      }

      return updatedJob;
    } catch (error: unknown) {
      // Rethrow BadRequestError
      if (error instanceof Error && error.name === 'BadRequestError') {
        throw error;
      }

      // Convert unknown errors to DatabaseError
      log.error(`Error incrementing successful rows for contact bulk import job with ID: ${jobId} for organization: ${organizationId}`, error);
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Failed to increment successful rows',
        'BULK_IMPORT_013',
        error instanceof Error ? [{ field: 'database', message: error.message }] : undefined
      );
    }
  }
}

import { createClient, RedisClientType } from 'redis';
import dotenv from 'dotenv';
import config from '../../config';
import {logger} from '../../logger';

dotenv.config();

const log = logger(config.logger);

const redisClientOptions = {
    socket: {
        host: process.env.REDIS_HOST,
        port: Number(process.env.REDIS_PORT),
    },
    password: process.env.REDIS_PASSWORD,
};

const client: RedisClientType = createClient(redisClientOptions);

client.on('error', (err:any) => log.error('Redis Client Error', err));
client.connect();

class RedisConnector {
    static getConfig() {
        return redisClientOptions;
    }

    static async get(key: string): Promise<string | null> {
        return await client.get(key);
    }

    static async del(...keys: string[]): Promise<number> {
        return await client.del(keys);
    }

    static async set(key: string, value: string): Promise<(string | null)> {
        return await client.set(key, value);
    }

    static async mGet(...keys: string[]): Promise<(string | null)[]> {
        return await client.mGet(keys);
    }

    static async mSet(kvPairs: Record<string, string>): Promise<'OK'> {
        const flatPairs = Object.entries(kvPairs).flat();
        return await client.mSet(flatPairs as string[]);
    }

    static async expire(key: string, seconds: number): Promise<number> {
        return await client.expire(key, seconds);
    }

    static async scanAndDelete(pattern: string): Promise<void> {
        try {
            let cursor:string = "0";
            do {
                const reply = await client.scan(cursor, {
                    MATCH: pattern,
                    COUNT: 1000,
                });
                cursor = String(reply.cursor);
                const keys = reply.keys;
                if (keys.length > 0) await client.del(keys);
            } while (cursor !== '0');
        } catch (e) {
            log.error(e);
        }
    }

    static async closeConnection(): Promise<void> {
        try {
            await client.quit();
        } catch (err) {
            log.error('Error closing Redis connection', err);
            throw err;
        }
    }
}

export default RedisConnector;

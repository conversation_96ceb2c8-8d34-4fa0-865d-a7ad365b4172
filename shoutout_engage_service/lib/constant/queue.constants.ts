import { QUEUES, DEFAULT_QUEUE_OPTIONS, JOB_STATUS, JOB_CATEGORY } from '../../types/queue.types';
import { ERROR_REPORT_TYPE } from '../../types/error.types';
import { 
    FieldMapping, 
    BULK_IMPORT_STATUS, 
    BULK_IMPORT_ERROR_TYPE, 
    BulkImportError, 
    BulkImportResult 
} from '../../types/contact.types';

// Re-export values and enums for backward compatibility
export {
    QUEUES,
    DEFAULT_QUEUE_OPTIONS,
    JOB_STATUS,
    JOB_CATEGORY,
    ERROR_REPORT_TYPE,
    BULK_IMPORT_STATUS,
    BULK_IMPORT_ERROR_TYPE
};

// Re-export interfaces for backward compatibility
export type {
    FieldMapping,
    BulkImportError,
    BulkImportResult
};
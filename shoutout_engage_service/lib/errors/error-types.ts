import { AppError } from './app-error';

/**
 * Error for duplicate resource conflicts (e.g., duplicate contact)
 * HTTP Status Code: 409 Conflict
 */
export class DuplicateError extends AppError {
  constructor(
    message: string = 'Resource already exists',
    errorCode: string = 'DUPLICATE_001',
    details?: Array<{ field: string; message: string }>
  ) {
    super(message, 409, errorCode, true, details);
  }
}

/**
 * Error for validation failures
 * HTTP Status Code: 422 Unprocessable Entity
 */
export class ValidationError extends AppError {
  constructor(
    message: string = 'Validation failed',
    errorCode: string = 'VALIDATION_001',
    details?: Array<{ field: string; message: string }>
  ) {
    super(message, 422, errorCode, true, details);
  }
}

/**
 * Error for bad requests (e.g., missing required parameters)
 * HTTP Status Code: 400 Bad Request
 */
export class BadRequestError extends AppError {
  constructor(
    message: string = 'Bad request',
    errorCode: string = 'BAD_REQUEST_001',
    details?: Array<{ field: string; message: string }>
  ) {
    super(message, 400, errorCode, true, details);
  }
}

/**
 * Error for organization-related issues
 * HTTP Status Code: 400 Bad Request
 */
export class OrganizationError extends AppError {
  constructor(
    message: string = 'Organization error',
    errorCode: string = 'ORG_001',
    details?: Array<{ field: string; message: string }>
  ) {
    super(message, 400, errorCode, true, details);
  }
}

/**
 * Error for database operations
 * HTTP Status Code: 500 Internal Server Error
 */
export class DatabaseError extends AppError {
  constructor(
    message: string = 'Database operation failed',
    errorCode: string = 'DB_001',
    details?: Array<{ field: string; message: string }>
  ) {
    super(message, 500, errorCode, true, details);
  }
}

/**
 * Error for not found resources
 * HTTP Status Code: 404 Not Found
 */
export class NotFoundError extends AppError {
  constructor(
    message: string = 'Resource not found',
    errorCode: string = 'NOT_FOUND_001',
    details?: Array<{ field: string; message: string }>
  ) {
    super(message, 404, errorCode, true, details);
  }
}

/**
 * Error for unauthorized access
 * HTTP Status Code: 401 Unauthorized
 */
export class UnauthorizedError extends AppError {
  constructor(
    message: string = 'Unauthorized access',
    errorCode: string = 'UNAUTHORIZED_001',
    details?: Array<{ field: string; message: string }>
  ) {
    super(message, 401, errorCode, true, details);
  }
}

/**
 * Error for forbidden access
 * HTTP Status Code: 403 Forbidden
 */
export class ForbiddenError extends AppError {
  constructor(
    message: string = 'Forbidden access',
    errorCode: string = 'FORBIDDEN_001',
    details?: Array<{ field: string; message: string }>
  ) {
    super(message, 403, errorCode, true, details);
  }
}

/**
 * Generic internal server error
 * HTTP Status Code: 500 Internal Server Error
 */
export class InternalError extends AppError {
  constructor(
    message: string = 'Internal server error',
    errorCode: string = 'INTERNAL_001',
    isOperational: boolean = false,
    details?: Array<{ field: string; message: string }>
  ) {
    super(message, 500, errorCode, isOperational, details);
  }
}
/**
 * Error codes for the application
 */

/**
 * Error codes for file validation
 */
export const FILE_ERROR_CODES = {
  INVALID_EXTENSION: 'FILE_001',
  FILE_TOO_LARGE: 'FILE_002',
  TOO_MANY_ROWS: 'FILE_003',
  MULTIPLE_SHEETS: 'FILE_004',
  NO_HEADERS: 'FILE_005',
  PROCESSING_ERROR: 'FILE_006'
};

/**
 * Error codes for contact operations
 */
export const CONTACT_ERROR_CODES = {
  DUPLICATE: 'CONTACT_001',
  INVALID_ID: 'CONTACT_002',
  NOT_FOUND: 'CONTACT_003',
  VALIDATION: 'CONTACT_004',
  BULK_IMPORT: 'CONTACT_005'
};

/**
 * Error codes for database operations
 */
export const DB_ERROR_CODES = {
  GENERAL: 'DB_001',
  CREATE: 'DB_002',
  READ: 'DB_003',
  UPDATE: 'DB_004',
  DELETE: 'DB_005',
  BULK_OPERATION: 'DB_006'
};

/**
 * Error codes for authentication and authorization
 */
export const AUTH_ERROR_CODES = {
  MISSING_HEADER: 'AUTH_001',
  MISSING_TOKEN: 'AUTH_002',
  INVALID_TOKEN: 'AUTH_003',
  EXPIRED_TOKEN: 'AUTH_004',
  INSUFFICIENT_PERMISSIONS: 'AUTH_005'
};

/**
 * Error codes for organization operations
 */
export const ORG_ERROR_CODES = {
  MISSING_CONTEXT: 'ORG_001',
  MISSING_USER: 'ORG_002',
  INVALID_ORGANIZATION: 'ORG_003'
};

/**
 * All error codes combined
 */
export const ERROR_CODES = {
  ...FILE_ERROR_CODES,
  ...CONTACT_ERROR_CODES,
  ...DB_ERROR_CODES,
  ...AUTH_ERROR_CODES,
  ...ORG_ERROR_CODES
};
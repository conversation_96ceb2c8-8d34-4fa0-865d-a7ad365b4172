import { ErrorResponse } from '../../types/common.types';

/**
 * Base application error class that extends the built-in Error class
 * Provides additional properties for HTTP status code, error code, and operational status
 */
export class AppError extends Error {
  /**
   * HTTP status code for the error
   */
  public statusCode: number;

  /**
   * Error code for identifying the error type
   */
  public errorCode: string;

  /**
   * Whether the error is operational (expected) or programming (unexpected)
   * Operational errors are expected errors that should be handled gracefully
   * Programming errors are unexpected errors that indicate a bug in the code
   */
  public isOperational: boolean;

  /**
   * Optional details for field-specific errors
   */
  public details?: Array<{
    field: string;
    message: string;
  }>;

  /**
   * Create a new AppError
   * @param message - Error message
   * @param statusCode - HTTP status code
   * @param errorCode - Error code for identifying the error type
   * @param isOperational - Whether the error is operational (expected) or programming (unexpected)
   * @param details - Optional details for field-specific errors
   */
  constructor(
    message: string,
    statusCode: number = 500,
    errorCode: string = 'INTERNAL_001',
    isOperational: boolean = true,
    details?: Array<{ field: string; message: string }>
  ) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.isOperational = isOperational;
    this.details = details;

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Convert the error to an ErrorResponse object
   * @returns ErrorResponse object
   */
  toErrorResponse(): ErrorResponse {
    const response: ErrorResponse = {
      error: this.message,
      errorCode: this.errorCode
    };

    if (this.details) {
      response.details = this.details;
    }

    return response;
  }
}
import { Response } from 'express';
import { AppError } from './app-error';
import {
  DatabaseError, 
  DuplicateError,
  InternalError,
  OrganizationError,
  ValidationError 
} from './error-types';
import { logger } from '../logger';
import config from '../config';
import { ErrorResponse } from '../../types/common.types';

const log = logger(config.logger);

/**
 * Centralized error handler for Express applications
 * Handles different types of errors and sends appropriate responses
 * 
 * @param error - The error object
 * @param res - Express response object
 */
export function handleError(error: any, res: Response): void {
  // Log the error
  log.error('Error:', error);

  // If it's already an AppError, use its properties
  if (error instanceof AppError) {
    res.status(error.statusCode).json(error.toErrorResponse());
    return;
  }

  // Handle MongoDB duplicate key errors
  if (error.name === 'MongoError' && error.code === 11000) {
    const duplicateError = new DuplicateError(
      'Resource already exists',
      'DUPLICATE_001'
    );
    res.status(duplicateError.statusCode).json(duplicateError.toErrorResponse());
    return;
  }

  // Handle Mongoose validation errors
  if (error.name === 'ValidationError') {
    const details = Object.keys(error.errors).map(field => ({
      field,
      message: error.errors[field].message
    }));

    const validationError = new ValidationError(
      'Validation failed',
      'VALIDATION_001',
      details
    );
    res.status(validationError.statusCode).json(validationError.toErrorResponse());
    return;
  }

  // Try to identify the error type based on the error message
  if (error instanceof Error) {
    // Handle duplicate contact errors
    if (error.message.includes('already exists')) {
      const duplicateError = new DuplicateError(
        error.message,
        'CONTACT_001'
      );
      res.status(duplicateError.statusCode).json(duplicateError.toErrorResponse());
      return;
    }

    // Handle validation errors
    if (error.message.includes('Validation failed')) {
      const validationError = new ValidationError(
        error.message,
        'CONTACT_002'
      );
      res.status(validationError.statusCode).json(validationError.toErrorResponse());
      return;
    }

    // Handle organization-related errors
    if (error.message.includes('organization')) {
      const orgError = new OrganizationError(
        error.message,
        'ORG_002'
      );
      res.status(orgError.statusCode).json(orgError.toErrorResponse());
      return;
    }

    // Handle database errors
    if (error.message.toLowerCase().includes('database')) {
      const dbError = new DatabaseError(
        'Database operation failed',
        'DB_001'
      );
      res.status(dbError.statusCode).json(dbError.toErrorResponse());
      return;
    }
  }

  // Default to internal server error for unhandled errors
  const internalError = new InternalError(
    'Internal server error',
    'INTERNAL_001',
    false
  );
  res.status(internalError.statusCode).json(internalError.toErrorResponse());
}

/**
 * Convert any error to an ErrorResponse object
 * Useful for logging or when you need the error details but don't want to send a response yet
 * 
 * @param error - The error object
 * @returns ErrorResponse object
 */
export function errorToErrorResponse(error: any): ErrorResponse {
  if (error instanceof AppError) {
    return error.toErrorResponse();
  }

  // Default error response
  return {
    error: error instanceof Error ? error.message : 'Unknown error',
    errorCode: 'INTERNAL_001'
  };
}
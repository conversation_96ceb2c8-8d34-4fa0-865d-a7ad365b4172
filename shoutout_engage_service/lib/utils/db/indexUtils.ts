import mongoose, { Model, Schema } from 'mongoose';
import MongooseConnector from '../../db/connectors/MongooseConnector';
import { logger } from '../../logger';
import config from '../../config';

const log = logger(config.logger);

/**
 * Ensures that all indexes defined in a model's schema are created in MongoDB.
 * This function checks if the collection exists and if all required indexes are already created.
 * If not, it creates the indexes.
 * 
 * This function should be called after the MongoDB connection is established.
 * It will only execute in non-test environments, as indexes in test environments
 * are created explicitly in tests/setup.ts.
 * 
 * @param model - The Mongoose model for which to ensure indexes
 * @param schema - The Mongoose schema associated with the model
 * @param modelName - A descriptive name for the model (used in logs)
 * 
 * @example
 * ```typescript
 * // In your model file
 * import { ensureIndexes } from '../utils/db/indexUtils';
 * 
 * const userSchema = new Schema({
 *   email: { type: String, required: true, index: true },
 *   // ... other fields
 * });
 * 
 * export const UserModel = model<UserDocument>('User', userSchema);
 * 
 * // Ensure indexes are created
 * ensureIndexes(UserModel, userSchema, 'User');
 * ```
 */
export const ensureIndexes = (
  model: Model<any>,
  schema: Schema,
  modelName: string
): void => {
  // Skip index creation in test environments
  if (process.env.NODE_ENV === 'test') {
    return;
  }

  // Get the collection name from the model
  const collectionName = model.collection.name;

  // Check if collection exists and create indexes only if needed
  mongoose.connection.once('open', async () => {
    try {
      // Get the MongoDB connection
      const connection = MongooseConnector.getConnection();
      
      if (!connection) {
        log.warn(`MongoDB connection not available, skipping index creation for ${modelName} model`);
        return;
      }

      // Check if db property is available
      if (!connection.db) {
        log.warn(`MongoDB db object not available, skipping index creation for ${modelName} model`);
        return;
      }

      // Get list of all collections
      const db = connection.db!; // Non-null assertion
      const collections = await db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);
      
      // Check if our collection exists
      const collectionExists = collectionNames.includes(collectionName);
      
      if (!collectionExists) {
        // Collection doesn't exist, create indexes
        await model.createIndexes();
        log.info(`${modelName} model indexes created successfully for new collection: ${collectionName}`);
      } else {
        // Collection exists, check if it has indexes
        // We've already checked connection.db above, but TypeScript might still complain
        // so we'll add an additional type assertion here
        const db = connection.db!; // Non-null assertion
        const indexInfo = await db.collection(collectionName).indexInformation();
        
        // Get the number of indexes defined in the schema
        // Count the number of index definitions in the schema (excluding _id which is added automatically)
        const schemaIndexCount = Object.keys(schema.indexes()).length + 
                               Object.values(schema.paths)
                                 .filter(path => path.options && path.options.index)
                                 .length;
        
        // If collection has fewer indexes than defined in the schema, create them
        if (Object.keys(indexInfo).length < schemaIndexCount + 1) { // +1 for _id index
          await model.createIndexes();
          log.info(`${modelName} model indexes created successfully for existing collection: ${collectionName}`);
        } else {
          log.info(`${modelName} model indexes already exist for collection: ${collectionName}, skipping creation`);
        }
      }
    } catch (err) {
      log.error(`Error checking or creating ${modelName} model indexes:`, err instanceof Error ? err.message : String(err));
    }
  });
};
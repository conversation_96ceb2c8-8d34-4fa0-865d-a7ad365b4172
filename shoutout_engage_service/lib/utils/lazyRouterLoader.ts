import { Request, Response, NextFunction, Router } from 'express';

export const lazyLoadRoute = (loader: () => Promise<{ default: Router }>) => {
    let cachedRouter: Router | null = null;

    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            if (!cachedRouter) {
                const module = await loader();
                cachedRouter = module.default;
            }
            return cachedRouter(req, res, next);
        } catch (err) {
            next(err);
        }
    };
};

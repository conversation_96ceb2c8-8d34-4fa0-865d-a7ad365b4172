import { logger } from '../logger';
import config from '../config';

const log = logger(config.logger);

/**
 * Email template types
 */
export enum EmailTemplate {
  BULK_IMPORT_COMPLETION = 'bulk-import-completion',
  BULK_IMPORT_ERROR_REPORT = 'bulk-import-error-report'
}

/**
 * Template loader for email templates
 */
export class TemplateLoader {
  /**
   * Templates stored as string literals
   */
  private static templates: Record<EmailTemplate, string> = {
    [EmailTemplate.BULK_IMPORT_COMPLETION]: `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            .status { display: inline-block; padding: 8px 16px; border-radius: 4px; font-weight: bold; color: white; }
            .stats { margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 4px; }
            .stats-item { margin-bottom: 10px; }
            .progress-bar { height: 20px; background-color: #e0e0e0; border-radius: 10px; overflow: hidden; }
            .progress-fill { height: 100%; background-color: #4caf50; }
            .footer { margin-top: 30px; font-size: 12px; color: #777; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Contact Import {{statusText}}</h2>
              <div class="status" style="background-color: {{statusColor}};">{{statusText}}</div>
            </div>
            
            <p>Your contact import for file <strong>{{fileName}}</strong> has {{completionText}} with the following results:</p>
            
            <div class="stats">
              <div class="stats-item">
                <strong>Total Rows:</strong> {{totalRows}}
              </div>
              <div class="stats-item">
                <strong>Processed Rows:</strong> {{processedRows}} ({{processedPercentage}}%)
                <div class="progress-bar">
                  <div class="progress-fill" style="width: {{processedPercentage}}%"></div>
                </div>
              </div>
              <div class="stats-item">
                <strong>Successfully Imported:</strong> {{successfulRows}} ({{successPercentage}}%)
                <div class="progress-bar">
                  <div class="progress-fill" style="width: {{successPercentage}}%"></div>
                </div>
              </div>
              <div class="stats-item">
                <strong>Failed Rows:</strong> {{failedRows}} ({{failedPercentage}}%)
              </div>
            </div>
            
            {{errorSummary}}
            
            <p>Thank you for using ShoutOUT Engage!</p>
            
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `,
    [EmailTemplate.BULK_IMPORT_ERROR_REPORT]: `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 800px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
            th { background-color: #f2f2f2; text-align: left; }
            td, th { padding: 8px; border: 1px solid #ddd; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            .footer { margin-top: 30px; font-size: 12px; color: #777; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Contact Import Error Report</h2>
            </div>
            
            <p>The following errors were encountered while importing contacts from <strong>{{fileName}}</strong>:</p>
            
            {{errorTables}}
            
            <p>Please correct these errors and try importing again.</p>
            
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `
  };

  /**
   * Compile a template with variables
   * @param template Template to compile
   * @param variables Variables to replace in the template
   * @returns Compiled template
   */
  public static compile(template: EmailTemplate, variables: Record<string, any>): string {
    try {
      log.debug(`Compiling template: ${template}`);
      
      // Get the template string
      let templateString = this.templates[template];
      
      // Replace variables in the template
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        templateString = templateString.replace(regex, String(value));
      });
      
      return templateString;
    } catch (error) {
      log.error(`Error compiling template ${template}:`, error);
      throw error;
    }
  }

  /**
   * Generate error summary HTML for bulk import completion email
   * @param errors Array of errors
   * @returns HTML string with error summary
   */
  public static generateErrorSummary(errors: any[]): string {
    if (!errors || errors.length === 0) {
      return '';
    }

    // Count errors by type
    const errorTypes: Record<string, number> = {};
    errors.forEach(error => {
      if (!errorTypes[error.type]) {
        errorTypes[error.type] = 0;
      }
      errorTypes[error.type]++;
    });

    // Generate HTML
    const errorItems = Object.entries(errorTypes)
      .map(([type, count]) => `<li>${this.formatErrorType(type)}: ${count} errors</li>`)
      .join('');

    return `
      <h3>Error Summary:</h3>
      <ul>
        ${errorItems}
      </ul>
      <p>Please check the detailed error report for more information.</p>
    `;
  }

  /**
   * Generate error tables HTML for bulk import error report email
   * @param errors Array of errors
   * @returns HTML string with error tables
   */
  public static generateErrorTables(errors: any[]): string {
    if (!errors || errors.length === 0) {
      return '<p>No errors were found.</p>';
    }

    // Group errors by type
    const errorsByType: Record<string, any[]> = {};
    errors.forEach(error => {
      if (!errorsByType[error.type]) {
        errorsByType[error.type] = [];
      }
      errorsByType[error.type].push(error);
    });

    // Generate HTML tables by type
    return Object.entries(errorsByType)
      .map(([type, typeErrors]) => {
        const rows = typeErrors.map(error => `
          <tr>
            <td>${error.row}</td>
            <td>${error.field || 'N/A'}</td>
            <td>${error.value || 'N/A'}</td>
            <td>${error.message}</td>
          </tr>
        `).join('');

        return `
          <h3>${this.formatErrorType(type)} Errors (${typeErrors.length})</h3>
          <table border="1" cellpadding="5" cellspacing="0" width="100%">
            <thead>
              <tr style="background-color: #f2f2f2;">
                <th>Row</th>
                <th>Field</th>
                <th>Value</th>
                <th>Message</th>
              </tr>
            </thead>
            <tbody>
              ${rows}
            </tbody>
          </table>
        `;
      })
      .join('<br>');
  }

  /**
   * Format error type for display
   * @param errorType Error type
   * @returns Formatted error type
   */
  private static formatErrorType(errorType: string): string {
    return errorType.charAt(0).toUpperCase() + errorType.slice(1).toLowerCase();
  }
}
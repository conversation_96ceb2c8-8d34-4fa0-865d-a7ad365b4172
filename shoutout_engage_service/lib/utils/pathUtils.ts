import { fileURLToPath } from 'url';
import path from 'path';

/**
 * Utility function to get __filename and __dirname equivalents in ES modules
 * @param importMetaUrl - import.meta.url from the calling module
 * @returns Object containing __filename and __dirname
 */
export function getDirname(importMetaUrl: string) {
    const __filename = fileURLToPath(importMetaUrl);
    const __dirname = path.dirname(__filename);

    return { __filename, __dirname };
}

/**
 * Safe version of getDirname that works in both runtime and test environments
 * @param importMetaUrl - import.meta.url from the calling module (optional in test env)
 * @returns Object containing __filename and __dirname
 */
export function getSafeDirname(importMetaUrl?: string) {
    // In test environment, import.meta.url might not be available
    if (!importMetaUrl) {
        // Fallback for test environment - use current working directory
        const __filename = path.resolve(process.cwd(), 'lib/config.ts');
        const __dirname = path.dirname(__filename);
        return { __filename, __dirname };
    }

    return getDirname(importMetaUrl);
}

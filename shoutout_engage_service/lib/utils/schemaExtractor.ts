import { ContactSchemaField } from '../../types/contact.types';

/**
 * Utility class for extracting schema information from contact model
 */
export class SchemaExtractor {
  /**
   * Get contact fields from the contact schema
   * @returns Array of contact schema fields with type information
   */
  public static getContactFields(): ContactSchemaField[] {
    // Define the contact fields based on the Contact interface
    // This could be enhanced to dynamically extract from Mongoose schema
    return [
      {
        name: 'name',
        type: 'string',
        required: true,
        description: 'Contact full name'
      },
      {
        name: 'email',
        type: 'string',
        required: true,
        description: 'Contact email address'
      },
      {
        name: 'phone',
        type: 'string',
        required: true,
        description: 'Contact phone number'
      },
      {
        name: 'country',
        type: 'string',
        required: false,
        description: 'Country name'
      },
      {
        name: 'country_code',
        type: 'string',
        required: false,
        description: 'ISO country code (2 letters)'
      },
      {
        name: 'avatar_url',
        type: 'string',
        required: false,
        description: 'URL to contact avatar image'
      },
      {
        name: 'tags',
        type: 'array',
        required: false,
        description: 'Contact tags'
      },
      {
        name: 'additional_fields',
        type: 'object',
        required: false,
        description: 'Custom fields for the contact'
      }
    ];
  }
}

import { OAS3Options } from 'swagger-jsdoc';
import envConfig from '../config';

class Swagger {
    static getOptions(): OAS3Options {
        return {
            definition: {
                openapi: '3.0.0',
                info: {
                    title: 'ShoutOUT Engage Service',
                    version: '1.0.0',
                    description:
                        'This is the engage service api under the ShoutOUT enterprise services stack',
                    contact: {
                        name: 'ShoutOUT',
                        url: 'https://getshoutout.com/',
                        email: '<EMAIL>',
                    },
                },
                servers: [
                    {
                        url: envConfig.SWAGGER_SERVER_URL || 'http://localhost:3001/api/engageservice',
                    },
                ],
                components: {
                    securitySchemes: {
                        Token: {
                            scheme: 'bearer',
                            type: 'http',
                            in: 'header',
                        },
                    },
                },
            },
            apis: ['./routes/*.ts'],
        };
    }
}

export default Swagger;

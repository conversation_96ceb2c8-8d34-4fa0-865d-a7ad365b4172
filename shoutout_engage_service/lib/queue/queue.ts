import Queue from 'bull';
import { RedisConnector } from '../db/connectors/RedisConnector';
import { QUEUES, DEFAULT_QUEUE_OPTIONS } from '../constant/queue.constants';
import { logger } from '../logger';
import config from '../config';

const log = logger(config.logger);

/**
 * Contact bulk import queue for processing CSV uploads
 */
const contactBulkImportQueue = new Queue(
  QUEUES.CONTACT_BULK_IMPORT_QUEUE,
  {
    redis: RedisConnector.getConfig(),
    defaultJobOptions: DEFAULT_QUEUE_OPTIONS
  }
);

/**
 * Contact bulk import processor queue for processing individual contacts
 */
const contactBulkImportProcessorQueue = new Queue(
  QUEUES.CONTACT_BULK_IMPORT_PROCESSOR_QUEUE,
  {
    redis: RedisConnector.getConfig(),
    defaultJobOptions: DEFAULT_QUEUE_OPTIONS
  }
);

/**
 * Email queue for sending emails through various providers
 */
const emailQueue = new Queue(
  QUEUES.EMAIL_QUEUE,
  {
    redis: RedisConnector.getConfig(),
    defaultJobOptions: DEFAULT_QUEUE_OPTIONS
  }
);

// Set up event handlers for queues
const setupQueueEvents = (queue: Queue.Queue, queueName: string) => {
  queue.on('error', (error) => {
    log.error(`${queueName} queue error:`, error);
  });

  queue.on('failed', (job, error) => {
    log.error(`${queueName} job ${job.id} failed:`, error);
  });

  queue.on('completed', (job) => {
    log.info(`${queueName} job ${job.id} completed`);
  });

  queue.on('stalled', (job) => {
    log.warn(`${queueName} job ${job.id} stalled`);
  });
};

// Set up event handlers for all queues
setupQueueEvents(contactBulkImportQueue, 'Contact bulk import');
setupQueueEvents(contactBulkImportProcessorQueue, 'Contact bulk import processor');
setupQueueEvents(emailQueue, 'Email');

/**
 * Clean up queues on process exit
 */
const cleanupQueues = async () => {
  try {
    log.info('Closing Bull queues...');
    await contactBulkImportQueue.close();
    await contactBulkImportProcessorQueue.close();
    await emailQueue.close();
    log.info('Bull queues closed');
  } catch (error) {
    log.error('Error closing Bull queues:', error);
  }
};

// Handle process termination
process.on('SIGTERM', cleanupQueues);
process.on('SIGINT', cleanupQueues);

export {
  contactBulkImportQueue,
  contactBulkImportProcessorQueue,
  emailQueue,
  cleanupQueues
};
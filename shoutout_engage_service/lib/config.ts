'use strict';
import path from 'node:path';
import * as yaml_config from 'node-yaml-config';
import fs from 'fs';
import { getSafeDirname } from './utils/pathUtils';

// Get the directory of this file using the safe utility (without import.meta.url for test compatibility)
const { __dirname: currentDir } = getSafeDirname();

// Function to find the project root by looking for package.json
function findProjectRoot(startDir: string): string {
    let currentDir = startDir;

    while (currentDir !== path.dirname(currentDir)) {
        const packageJsonPath = path.join(currentDir, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            return currentDir;
        }
        currentDir = path.dirname(currentDir);
    }

    // Fallback to current working directory
    return process.cwd();
}

// Find project root and construct config path
const projectRoot = findProjectRoot(currentDir);
const fileName = 'common.yml';
const configPath = path.resolve(projectRoot, 'config', fileName);



// Validate config file exists and load config
if (!fs.existsSync(configPath)) {
    throw new Error(`Configuration file not found at: ${configPath}`);
}

// Load config with environment support
const environment = process.env.NODE_ENV || 'development';
const rawConfig = yaml_config.load(configPath);

// Get environment-specific config or fallback to defaults
const config = rawConfig[environment] || rawConfig.defaults || rawConfig;

// Validate that config has required properties
if (!config || typeof config !== 'object') {
    throw new Error('Invalid configuration loaded from YAML file');
}

if (!config.api || !config.api.base_path) {
    throw new Error('Missing required configuration: api.base_path');
}

export default config;

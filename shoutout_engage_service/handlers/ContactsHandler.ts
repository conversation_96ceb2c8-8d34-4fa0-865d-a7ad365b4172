import { Request, Response } from 'express';
import { ObjectId } from 'mongodb';
import { ContactDAO } from '../lib/db/dao/ContactDAO';
import { ContactStatus, CreateContactData, ContactCreateRequest, ContactResponse, ContactsQueryParams, PaginatedContactsResponse } from '../types/contact.types';
import { logger } from '../lib/logger';
import config from '../lib/config';
import { AuthenticatedRequest } from '../lib/middlewares/supabase.authorizer.middleware';
import {CSVService} from "../services/csv.service";
import {CSVHeaderExtractionResponse, FileErrorResponse} from "../types/file.types";
import {ERROR_CODES} from "../lib/constant/file.constants";
import {SchemaExtractor} from "../lib/utils/schemaExtractor";
import { handleError } from '../lib/errors/error-handler';
import {
  <PERSON><PERSON>rror,
  OrganizationError,
  BadRequestError,
} from '../lib/errors/error-types';
import { contactBulkImportQueue } from '../lib/queue/queue';


const log = logger(config.logger);

/**
 * Handler class for contact-related business logic
 */
export class ContactsHandler {
  /**
   * Extract headers from a CSV or Excel file
   * @param req Express request with file from multer
   * @param res Express response
   * @returns Promise with extracted headers and contact schema
   */
  public static async extractCSVHeaders(req: Request & { file?: Express.Multer.File }, res: Response) {
    try {
      // Get the uploaded file from multer middleware
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded',
          errorCode: 'FILE_MISSING'
        } as FileErrorResponse);
      }

      // Validate file structure
      const validationResult = await CSVService.validateFileStructure(file);

      if (!validationResult.isValid) {
        // Use the error code directly from the validation result
        const errorCode = validationResult.errorCode || ERROR_CODES.PROCESSING_ERROR;

        return res.status(400).json({
          success: false,
          error: validationResult.errors.join('. '),
          errorCode,
          details: validationResult.errors.map(error => ({
            code: errorCode,
            message: error
          }))
        } as FileErrorResponse);
      }

      // Extract headers from the file
      const csvHeaders = await CSVService.extractHeaders(file);

      // Get contact schema fields
      const contactSchema = SchemaExtractor.getContactFields();

      // Return successful response with headers and schema
      return res.status(200).json({
        success: true,
        data: {
          csvHeaders,
          contactSchema,
          fileInfo: {
            name: file.originalname,
            size: file.size,
            rowCount: validationResult.rowCount || 0
          }
        }
      } as CSVHeaderExtractionResponse);

    } catch (error) {
      console.error('Error extracting CSV headers:', error);

      return res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Error processing file',
        errorCode: ERROR_CODES.PROCESSING_ERROR
      } as FileErrorResponse);
    }
  }

  /**
   * Create a new contact
   * @param req - Express request object with authenticated user and organization context
   * @param res - Express response object
   */
  static async createContact(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      log.info('Creating contact for user:', { 
        userId: req.userId, 
        organizationId: req.organizationId 
      });

      // Organization ID should be available from middleware
      if (!req.organizationId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      // Extract and validate request data
      const contactData = req.body as ContactCreateRequest;

      // Convert tag_id strings to ObjectIds if tags are provided
      const processedTags = contactData.tags?.map(tag => ({
        tag_id: new ObjectId(tag.tag_id),
        tag_name: tag.tag_name
      })) || [];

      // Prepare data for DAO
      const createData: CreateContactData = {
        org_id: req.organizationId,
        created_by: req.userId!,
        name: contactData.name,
        email: contactData.email,
        phone: contactData.phone,
        country: contactData.country,
        country_code: contactData.country_code,
        avatar_url: contactData.avatar_url,
        tags: processedTags,
        additional_fields: contactData.additional_fields || {},
        status: contactData.status || ContactStatus.ACTIVE
      };

      // Create contact via DAO
      const createdContact = await ContactDAO.createContact(createData);

      // Format response using DAO utility method
      const response: ContactResponse = ContactDAO.formatContactResponse(createdContact);

      log.info('Contact created successfully:', { 
        contactId: createdContact._id, 
        orgId: req.organizationId, 
        userId: req.userId 
      });

      res.status(201).json(response);

    } catch (error) {
      handleError(error, res);
    }
  }



  /**
   * Get contacts with pagination, filtering, sorting, and search capabilities
   * @param req - Express request object with authenticated user and organization context
   * @param res - Express response object
   */
  static async getContacts(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      log.info('Retrieving contacts for user:', { 
        userId: req.userId, 
        organizationId: req.organizationId 
      });

      // Extract and validate organization ID
      if (!req.organizationId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      // Process query parameters
      // Note: The validation middleware has already validated these parameters
      const params: ContactsQueryParams = {
        page: Number(req.query.page),
        page_size: Number(req.query.page_size),
        sort_by: req.query.sort_by as string || 'created_at',
        sort_direction: (req.query.sort_direction as 'asc' | 'desc') || 'desc',
        search: req.query.search as string,
        contactFilterQuery: req.query.contactFilterQuery as string
      };

      // Log the processed parameters for debugging
      log.debug('Processed query parameters:', params);

      try {
        // Call ContactDAO.getContacts with organization ID and processed parameters
        const { contacts, totalCount } = await ContactDAO.getContacts(
          req.organizationId,
          params
        );

        // Format contacts using existing formatContactsResponse method
        const formattedContacts = ContactDAO.formatContactsResponse(contacts);
        
        // Calculate pagination metadata
        const page = params.page || 1;
        const pageSize = params.page_size || 20;
        const totalPages = Math.ceil(totalCount / pageSize);
        
        // Build response with pagination metadata
        const response: PaginatedContactsResponse = {
          data: formattedContacts,
          pagination: {
            total_count: totalCount,
            page,
            page_size: pageSize,
            total_pages: totalPages,
            has_next_page: page < totalPages,
            has_prev_page: page > 1
          }
        };
        
        // Return 200 response with data and pagination
        log.info(`Successfully retrieved ${formattedContacts.length} contacts for organization: ${req.organizationId}`);
        res.status(200).json(response);
        
      } catch (dbError) {
        log.error('Database error while retrieving contacts:', dbError);
        
        // Throw a DatabaseError to be handled by the centralized error handler
        throw new DatabaseError('Failed to retrieve contacts', 'DB_001');
      }
    } catch (error) {
      handleError(error, res);
    }
  }

  /**
   * Get a contact by ID
   * @param req - Express request object with authenticated user and organization context
   * @param res - Express response object
   */
  static async getContactById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const contactId = req.params.id;
      
      log.info('Retrieving contact by ID:', { 
        contactId,
        userId: req.userId, 
        organizationId: req.organizationId 
      });

      // Extract and validate organization ID
      if (!req.organizationId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      try {
        // Call ContactDAO.getContactById with organization ID and contact ID
        const contact = await ContactDAO.getContactById(
          req.organizationId,
          contactId
        );

        // Format contact using existing formatContactResponse method
        const formattedContact = ContactDAO.formatContactResponse(contact);
        
        // Return 200 response with contact data
        log.info(`Successfully retrieved contact with ID: ${contactId} for organization: ${req.organizationId}`);
        res.status(200).json(formattedContact);
        
      } catch (dbError: unknown) {
        // If the error is already an AppError (NotFoundError, BadRequestError), rethrow it
        if (dbError instanceof Error && 
            (dbError.name === 'NotFoundError' || dbError.name === 'BadRequestError')) {
          throw dbError;
        }
        
        log.error('Database error while retrieving contact:', dbError);
        
        // Throw a DatabaseError to be handled by the centralized error handler
        throw new DatabaseError('Failed to retrieve contact', 'DB_001');
      }
    } catch (error) {
      handleError(error, res);
    }
  }

  /**
   * Bulk import contacts from a CSV or Excel file
   * @param req - Express request object with authenticated user, organization context, and file
   * @param res - Express response object
   */
  static async bulkImportContacts(req: AuthenticatedRequest & { file?: Express.Multer.File }, res: Response): Promise<void> {
    try {
      log.info('Processing bulk import request:', { 
        userId: req.userId, 
        organizationId: req.organizationId,
        fileName: req.file?.originalname
      });

      // Extract and validate organization ID and user ID
      if (!req.organizationId) {
        throw new OrganizationError('Organization context is required', 'ORG_001');
      }

      if (!req.userId) {
        throw new OrganizationError('User context is required', 'ORG_002');
      }

      // Get the uploaded file from multer middleware
      const file = req.file;
      if (!file) {
        throw new BadRequestError(
          'No file uploaded',
          'BULK_IMPORT_001',
          [{ field: 'file', message: 'File is required for bulk import' }]
        );
      }

      // Extract field mappings from request body
      const { fieldMappings } = req.body;

      try {
        // Validate file structure
        const validationResult = await CSVService.validateFileStructure(file);

        if (!validationResult.isValid) {
          throw new BadRequestError(
            validationResult.errors.join('. '),
            'BULK_IMPORT_002',
            validationResult.errors.map(error => ({
              field: 'file',
              message: error
            }))
          );
        }

        // Create a bulk import job
        const bulkImportJob = await ContactDAO.createBulkImportJob(
            req.organizationId,
            req.userId,
            `${req.organizationId}-${Date.now()}-${file.originalname}`,
            file.originalname,  // Original file name
            fieldMappings
        );

        // Add job to the queue
        await contactBulkImportQueue.add(
          {
            jobId: bulkImportJob._id.toString(),
            organizationId: req.organizationId,
            userId: req.userId,
            fileName: file.originalname,
            fieldMappings,
            fileBuffer: file.buffer.toString('base64') // Convert buffer to base64 string for queue
          },
          {
            jobId: bulkImportJob._id.toString()
          }
        );

        // Return success response with job ID
        log.info(`Successfully created bulk import job with ID: ${bulkImportJob._id} for organization: ${req.organizationId}`);
        res.status(202).json({
          success: true,
          message: 'Bulk import job created successfully',
          data: {
            jobId: bulkImportJob._id.toString(),
            status: bulkImportJob.status,
            fileName: file.originalname,
            createdAt: bulkImportJob.created_at
          }
        });
      } catch (dbError: unknown) {
        // If the error is already an AppError (BadRequestError), rethrow it
        if (dbError instanceof Error && dbError.name === 'BadRequestError') {
          throw dbError;
        }
        
        log.error('Error processing bulk import request:', dbError);
        
        // Throw a DatabaseError to be handled by the centralized error handler
        throw new DatabaseError(
          'Failed to process bulk import request',
          'DB_005',
          dbError instanceof Error ? [{ field: 'database', message: dbError.message }] : undefined
        );
      }
    } catch (error) {
      handleError(error, res);
    }
  }


  /**
   * Handle errors and send appropriate responses
   * @param error - The error object
   * @param res - Express response object
   * @deprecated Use the centralized error handler from lib/errors/error-handler.ts instead
   */
  private static handleError(error: any, res: Response): void {
    // Use the centralized error handler
    handleError(error, res);
  }
}
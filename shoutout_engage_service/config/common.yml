defaults: &defaults
  web:
    base_url: http://localhost:8080
  api:
    port: 3001
    base_url: http://localhost:3001
    base_path: /api/engageservice
  jwt:
    issuer: getshoutout.com
    audience: getshoutout.com
  email:
    host: smtp.sendgrid.net
    port: 587
    secure: false
  aws:
    document_download_url_expire_time: 3600
  swagger:
    server: http://localhost:3001/api/engageservice
  logger:
    name: ShoutOUT Engage Service
    level: debug
    levels:
      debug: STDOUT

development:
  <<: *defaults

test:
  <<: *defaults


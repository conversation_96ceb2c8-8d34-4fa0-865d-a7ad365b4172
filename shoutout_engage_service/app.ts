/**
 * Express application setup
 */

import path from 'node:path';
import createError from 'http-errors';
import express, { Request, Response, NextFunction } from 'express';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import helmet from 'helmet';
import http from 'http';

import swaggerUi from 'swagger-ui-express';
import swaggerJsdoc from 'swagger-jsdoc';


import config from './lib/config';
import {logger} from './lib/logger';

import indexRouter from './routes/index';
import swagger from './lib/swagger';
import MongooseConnector from './lib/db/connectors/MongooseConnector';
import PrismaConnector from "./lib/db/connectors/PrismaConnector";

import { lazyLoadRoute } from './lib/utils/lazyRouterLoader';
const log = logger(config.logger);

import {
    supabaseAuthMiddleware,
} from './lib/middlewares/supabase.authorizer.middleware';


const contactsRoute = lazyLoadRoute(() => import('./routes/contacts'));

const BASE_PATH = config.api.base_path;

const app = express();
const server = http.createServer(app);
// Use a simple path resolution that works in both runtime and test environments
const appDirname = process.cwd();

// Middleware
app.use(cors({ exposedHeaders: ['x-skip', 'x-limit', 'x-total'] }));
app.use(express.json({ limit: '5mb' }));
app.use(express.urlencoded({ extended: false, limit: '5mb' }));
app.use(cookieParser());
app.use(express.static(path.join(appDirname, 'public')));
app.disable('x-powered-by');
app.use(helmet());


// Swagger
app.use(`${BASE_PATH}/docs`, swaggerUi.serve, swaggerUi.setup(swaggerJsdoc(swagger.getOptions())));

// Logging
app.use((req: Request, res: Response, next: NextFunction) => {
    log.info('REQUEST:', {
        method: req.method,
        url: req.headers.host + req.originalUrl,
        origin: req.get('origin') || req.get('Origin'),
        body: req.body,
        queryParams: req.query,
    });
    next();
});
// Mount routers
app.use(`${BASE_PATH}/`, indexRouter);

// Dynamically import and apply all routes (abbreviated for readability)

const registerRoute = (
    path: string,
    routeModule: any,
    middleware: any = supabaseAuthMiddleware
) => {
    app.use(`${BASE_PATH}/${path}`, middleware, routeModule);
};

// Public routes (no authentication required)
const registerPublicRoute = (path: string, routeModule: any) => {
    app.use(`${BASE_PATH}/${path}`, routeModule);
};

// ⛳️ Use `registerRoute()` to cleanly apply routes
// Contacts routes
registerRoute('contacts', contactsRoute, supabaseAuthMiddleware);

// Public health check route
registerPublicRoute('health', (req: Request, res: Response) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// 404 Handler
app.use((req: Request, res: Response, next: NextFunction) => {
    log.info(`404 - Not Found: ${req.method} ${req.originalUrl}`);
    next(createError(404));
});

// Error Handler
app.use((err: any, req: Request, res: Response, next: NextFunction) => {
    if (err.err) err = err.err;
    log.error(err);

    let status: number;
    let message: string;
    const errorCode = err?.errorCode || (err.status ? null : '000500');

    if (err?.name === 'ForbiddenError') {
        status = 403;
        message = err.message;
    } else {
        status = err.status ? Number(err.status) : 500;
        message = err.status ? err.message : 'something went wrong';
    }

    res.status(status).json({ error: message, ...(errorCode ? { errorCode } : {}) });
});

export default async function createApp(skipDbInit: boolean = false): Promise<{ app: express.Application; server: http.Server }> {
    try {
        if (!skipDbInit) {
            await Promise.all([
                MongooseConnector.initialize(),
                PrismaConnector.initialize()
            ]);
        }
        return { app, server };
    } catch (e) {
        log.error(e);
        return Promise.reject(e);
    }
}

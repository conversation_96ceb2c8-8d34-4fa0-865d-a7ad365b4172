import Joi from 'joi';
import { ContactStatus } from '../types/contact.types';
import { ValidationError, BadRequestError } from '../lib/errors/error-types';
import { FieldMapping } from '../lib/constant/queue.constants';

/**
 * Joi validation schemas for contact-related operations
 */
export class ContactsValidator {
  /**
   * Schema for validating GET contacts query parameters
   */
  static readonly getContactsSchema = Joi.object({
    // Pagination parameters (required)
    page: Joi.number()
      .integer()
      .min(1)
      .required()
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1',
        'any.required': 'Page is required'
      }),
    
    page_size: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .required()
      .messages({
        'number.base': 'Page size must be a number',
        'number.integer': 'Page size must be an integer',
        'number.min': 'Page size must be at least 1',
        'number.max': 'Page size cannot exceed 100',
        'any.required': 'Page size is required'
      }),
    
    // Sorting parameters
    sort_by: Joi.string()
      .valid('name', 'email', 'phone', 'created_at', 'updated_at')
      .default('created_at')
      .messages({
        'any.only': 'Sort field must be one of: name, email, phone, created_at, updated_at'
      }),
    
    sort_direction: Joi.string()
      .valid('asc', 'desc')
      .default('desc')
      .messages({
        'any.only': 'Sort direction must be either asc or desc'
      }),
    
    // Search parameter
    search: Joi.string()
      .trim()
      .min(1)
      .messages({
        'string.min': 'Search term must not be empty'
      }),
    
    // MongoDB query for advanced filtering
    contactFilterQuery: Joi.string()
      .custom((value, helpers) => {
        try {
          const parsed = JSON.parse(value);
          
          // Basic validation of MongoDB query structure
          if (!parsed || typeof parsed !== 'object') {
            return helpers.error('string.mongoQuery.structure');
          }
          
          return parsed;
        } catch (error) {
          return helpers.error('string.mongoQuery.parse');
        }
      })
      .messages({
        'string.mongoQuery.parse': 'Contact filter query must be a valid JSON string',
        'string.mongoQuery.structure': 'Contact filter query must be a valid MongoDB query object'
      })
  }).options({
    stripUnknown: true,
    abortEarly: false
  });

  /**
   * Schema for validating contact creation requests
   */
  static readonly createContactSchema = Joi.object({
    // Required fields
    name: Joi.string()
      .trim()
      .min(1)
      .max(255)
      .required()
      .messages({
        'string.empty': 'Name is required',
        'string.min': 'Name must not be empty',
        'string.max': 'Name must not exceed 255 characters',
        'any.required': 'Name is required'
      }),

    email: Joi.string()
      .email({ tlds: { allow: false } })
      .trim()
      .lowercase()
      .required()
      .messages({
        'string.email': 'Email must be a valid email address',
        'string.empty': 'Email is required',
        'any.required': 'Email is required'
      }),

    phone: Joi.string()
      .trim()
      .pattern(/^[+]?[0-9\s\-\(\)]+$/)
      .min(7)
      .max(20)
      .required()
      .messages({
        'string.pattern.base': 'Phone number must contain only numbers, spaces, hyphens, parentheses, and optional plus sign',
        'string.min': 'Phone number must be at least 7 characters',
        'string.max': 'Phone number must not exceed 20 characters',
        'string.empty': 'Phone number is required',
        'any.required': 'Phone number is required'
      }),

    // Optional fields
    country: Joi.string()
      .trim()
      .max(100)
      .optional()
      .messages({
        'string.max': 'Country must not exceed 100 characters'
      }),

    country_code: Joi.string()
      .trim()
      .pattern(/^[A-Z]{2}$/)
      .optional()
      .messages({
        'string.pattern.base': 'Country code must be a 2-letter uppercase ISO code (e.g., US, CA, GB)'
      }),

    avatar_url: Joi.string()
      .uri({ scheme: ['http', 'https'] })
      .optional()
      .messages({
        'string.uri': 'Avatar URL must be a valid HTTP or HTTPS URL'
      }),

    // Tags array validation
    tags: Joi.array()
      .items(
        Joi.object({
          tag_id: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .required()
            .messages({
              'string.pattern.base': 'Tag ID must be a valid MongoDB ObjectId',
              'any.required': 'Tag ID is required'
            }),
          tag_name: Joi.string()
            .trim()
            .min(1)
            .max(100)
            .required()
            .messages({
              'string.empty': 'Tag name is required',
              'string.min': 'Tag name must not be empty',
              'string.max': 'Tag name must not exceed 100 characters',
              'any.required': 'Tag name is required'
            })
        })
      )
      .default([])
      .messages({
        'array.base': 'Tags must be an array'
      }),

    // Additional fields validation
    additional_fields: Joi.object()
      .pattern(
        Joi.string().min(1).max(50),
        Joi.alternatives().try(
          Joi.string().max(500),
          Joi.number(),
          Joi.boolean()
        )
      )
      .default({})
      .messages({
        'object.base': 'Additional fields must be an object',
        'object.pattern.match': 'Additional field values must be strings (max 500 chars), numbers, or booleans'
      }),

    // Status field (optional, defaults to active)
    status: Joi.string()
      .valid(...Object.values(ContactStatus))
      .default(ContactStatus.ACTIVE)
      .messages({
        'any.only': `Status must be one of: ${Object.values(ContactStatus).join(', ')}`
      })
  }).options({
    stripUnknown: true,
    abortEarly: false
  });

  /**
   * Schema for validating contact ID in route parameters
   */
  static readonly getContactByIdSchema = Joi.object({
    id: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.empty': 'Contact ID is required',
        'string.pattern.base': 'Contact ID must be a valid MongoDB ObjectId (24 hexadecimal characters)',
        'any.required': 'Contact ID is required'
      })
  }).options({
    stripUnknown: true,
    abortEarly: false
  });

  /**
   * Validates a contact creation request payload
   * @param payload - The request payload to validate
   * @returns Validation result with value and error
   */
  static validateCreateContact(payload: any) {
    return this.createContactSchema.validate(payload);
  }
  
  /**
   * Validates GET contacts query parameters
   * @param queryParams - The query parameters to validate
   * @returns Validation result with value and error
   * @throws BadRequestError if contactFilterQuery is not a valid JSON string
   */
  static validateGetContacts(queryParams: any) {
    // Handle special case for contactFilterQuery parameter
    if (queryParams.contactFilterQuery && typeof queryParams.contactFilterQuery === 'string') {
      try {
        // Try to parse the contactFilterQuery to ensure it's valid JSON
        JSON.parse(queryParams.contactFilterQuery);
      } catch (error) {
        // Throw BadRequestError for invalid JSON
        throw new BadRequestError(
          'Invalid contact filter query format',
          'QUERY_001',
          [{
            field: 'contactFilterQuery',
            message: 'Contact filter query must be a valid JSON string'
          }]
        );
      }
    }
    
    // Apply schema validation
    return this.getContactsSchema.validate(queryParams);
  }

  /**
   * Express middleware for validating contact creation requests
   * @param req - Express request object
   * @param res - Express response object
   * @param next - Express next function
   * @throws ValidationError if the request body fails validation
   */
  static validateCreateContactMiddleware(req: any, res: any, next: any) {
    const { error, value } = ContactsValidator.validateCreateContact(req.body);
    
    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      throw new ValidationError(
        'Validation failed for contact creation',
        'CONTACT_VALIDATION_001',
        validationErrors
      );
    }

    // Replace request body with validated and sanitized data
    req.body = value;
    next();
  }
  
  /**
   * Express middleware for validating GET contacts query parameters
   * @param req - Express request object
   * @param res - Express response object
   * @param next - Express next function
   * @throws ValidationError if the query parameters fail validation
   * @throws BadRequestError if contactFilterQuery is not a valid JSON string
   */
  static validateGetContactsMiddleware(req: any, res: any, next: any) {
    try {
      const { error, value } = ContactsValidator.validateGetContacts(req.query);
      
      if (error) {
        const validationErrors = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }));

        throw new ValidationError(
          'Invalid query parameters for contacts retrieval',
          'CONTACT_QUERY_VALIDATION_001',
          validationErrors
        );
      }

      // Handle special case for contactFilterQuery parameter - parse JSON string to object
      if (value.contactFilterQuery && typeof value.contactFilterQuery === 'string') {
        try {
          value.contactFilterQueryObject = JSON.parse(value.contactFilterQuery);
        } catch (error) {
          // This should never happen as we validate JSON parsing in validateGetContacts
          throw new BadRequestError(
            'Invalid contact filter query JSON',
            'QUERY_002',
            [{
              field: 'contactFilterQuery',
              message: 'Contact filter query must be a valid JSON string'
            }]
          );
        }
      }
      
      // Replace request query with validated and sanitized data
      req.query = value;
      next();
    } catch (error) {
      // Let the error propagate to the centralized error handler
      throw error;
    }
  }

  /**
   * Validates contact ID in route parameters
   * @param params - The route parameters to validate
   * @returns Validation result with value and error
   */
  static validateGetContactById(params: any) {
    return this.getContactByIdSchema.validate(params);
  }

  /**
   * Express middleware for validating contact ID in route parameters
   * @param req - Express request object
   * @param res - Express response object
   * @param next - Express next function
   * @throws ValidationError if the contact ID fails validation
   */
  static validateGetContactByIdMiddleware(req: any, res: any, next: any) {
    try {
      const { error, value } = ContactsValidator.validateGetContactById(req.params);
      
      if (error) {
        const validationErrors = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }));

        throw new ValidationError(
          'Invalid contact ID',
          'CONTACT_ID_VALIDATION_001',
          validationErrors
        );
      }

      // Replace request params with validated and sanitized data
      req.params = value;
      next();
    } catch (error) {
      // Let the error propagate to the centralized error handler
      throw error;
    }
  }

  /**
   * Schema for validating contact bulk import requests
   */
  static readonly bulkImportSchema = Joi.object({
    // Field mappings - array of objects mapping CSV columns to contact schema fields
    fieldMappings: Joi.array()
      .items(
        Joi.object({
          fileColumnName: Joi.string()
            .required()
            .messages({
              'string.empty': 'File column name is required',
              'any.required': 'File column name is required'
            }),
          systemAttributeName: Joi.string()
            .required()
            .messages({
              'string.empty': 'System attribute name is required',
              'any.required': 'System attribute name is required'
            })
        })
      )
      .min(1)
      .required()
      .messages({
        'array.min': 'At least one field mapping is required',
        'any.required': 'Field mappings are required'
      }),
    
    // Ensure required fields are mapped
    requiredFields: Joi.array()
      .items(Joi.string())
      .default(['name', 'email', 'phone'])
  }).options({
    stripUnknown: true,
    abortEarly: false
  });

  /**
   * Validates contact bulk import request
   * @param data - The request data to validate
   * @returns Validation result with value and error
   */
  static validateBulkImport(data: any) {
    const result = this.bulkImportSchema.validate(data);
    
    // Additional validation to ensure required fields are mapped
    if (!result.error && result.value) {
      const { fieldMappings, requiredFields } = result.value;
      const mappedSystemAttributes = fieldMappings.map((mapping: FieldMapping) => mapping.systemAttributeName);
      
      // Check if all required fields are mapped
      const missingRequiredFields = requiredFields.filter(
        (field: string) => !mappedSystemAttributes.includes(field)
      );
      
      if (missingRequiredFields.length > 0) {
        return {
          error: {
            details: missingRequiredFields.map((field:any) => ({
              path: ['fieldMappings'],
              message: `Required field '${field||""}' must be mapped`
            }))
          },
          value: result.value
        };
      }
    }
    
    return result;
  }

  /**
   * Express middleware for validating contact bulk import requests
   * @param req - Express request object
   * @param res - Express response object
   * @param next - Express next function
   * @throws ValidationError if the request body fails validation
   */
  static validateBulkImportMiddleware(req: any, res: any, next: any) {
    try {
      const { error, value } = ContactsValidator.validateBulkImport(req.body);
      
      if (error) {
        const validationErrors = error.details.map((detail: { path: any[]; message: any; }) => ({
          field: detail.path.join('.'),
          message: detail.message
        }));

        throw new ValidationError(
          'Validation failed for contact bulk import',
          'CONTACT_BULK_IMPORT_VALIDATION_001',
          validationErrors
        );
      }

      // Replace request body with validated and sanitized data
      req.body = value;
      next();
    } catch (error) {
      // Let the error propagate to the centralized error handler
      throw error;
    }
  }
}
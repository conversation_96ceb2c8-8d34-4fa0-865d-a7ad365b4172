import { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import path from 'path';
import { ERROR_CODES, FILE_CONSTRAINTS } from '../lib/constant/file.constants';

/**
 * File validation middleware class
 */
export class FileValidator {
  /**
   * Configure multer storage
   */
  private static storage = multer.memoryStorage();

  /**
   * Create multer upload instance
   */
  private static upload = multer({
    storage: this.storage,
    limits: {
      fileSize: FILE_CONSTRAINTS.maxSizeBytes
    },
    fileFilter: (req, file, cb) => {
      this.validateFileType(file, cb);
    }
  });

  /**
   * Validate file type based on extension
   */
  private static validateFileType(file: Express.Multer.File, cb: multer.FileFilterCallback): void {
    const ext = path.extname(file.originalname).toLowerCase();
    if (FILE_CONSTRAINTS.allowedExtensions.includes(ext)) {
      return cb(null, true);
    }
    const error = new Error(`Only ${FILE_CONSTRAINTS.allowedExtensions.join(', ')} files are allowed`) as any;
    error.code = ERROR_CODES.INVALID_EXTENSION;
    cb(error, false);
  }

  /**
   * Get middleware for file upload
   */
  public static getUploadMiddleware() {
    return this.upload.single('file');
  }

  /**
   * Handle multer errors
   */
  public static handleErrors(err: any, req: Request, res: Response, next: NextFunction) {
    if (err instanceof multer.MulterError) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          success: false,
          error: `File too large. Maximum size is ${FILE_CONSTRAINTS.maxSizeBytes / (1024 * 1024)}MB`,
          errorCode: ERROR_CODES.FILE_TOO_LARGE,
          details: [
            {
              code: ERROR_CODES.FILE_TOO_LARGE,
              message: `File too large. Please split your file or reduce its size.`,
              field: 'file'
            }
          ]
        });
      }
      return res.status(400).json({
        success: false,
        error: err.message,
        errorCode: 'FILE_ERROR'
      });
    }

    if (err) {
      return res.status(400).json({
        success: false,
        error: err.message,
        errorCode: err.code || 'FILE_ERROR'
      });
    }

    next();
  }

  /**
   * Validate that a file was uploaded
   */
  public static validateFilePresence(req: Request, res: Response, next: NextFunction) {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded',
        errorCode: 'FILE_MISSING'
      });
    }
    next();
  }
}

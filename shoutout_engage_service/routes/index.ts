import express, { Request, Response, NextFunction } from 'express';

const router = express.Router();

/* GET home page. */
router.get('/', function(req: Request, res: Response, next: NextFunction) {
    res.json({
        message: 'Welcome to ShoutOUT Engage API',
        title: 'ShoutOUT Engage API',
        version: '1.0.0',
        endpoints: {
            health: '/api/engageservice/health',
            docs: '/api/engageservice/docs',
            contacts: '/api/engageservice/contacts'
        }
    });
});

export default router;
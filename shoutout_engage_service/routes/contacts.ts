import express, {Response, NextFunction } from "express";
import { supabaseAuthMiddleware, AuthenticatedRequest } from "../lib/middlewares/supabase.authorizer.middleware";
import { ContactsValidator } from "../validators/ContactsValidator";
import { <PERSON>s<PERSON>and<PERSON> } from "../handlers/ContactsHandler";
import { FileValidator } from "../validators/FileValidator";

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     PaginatedContactsResponse:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ContactResponse'
 *           description: Array of contact objects
 *         pagination:
 *           type: object
 *           properties:
 *             total_count:
 *               type: integer
 *               description: Total number of contacts matching the query
 *               example: 42
 *             page:
 *               type: integer
 *               description: Current page number
 *               example: 1
 *             page_size:
 *               type: integer
 *               description: Number of contacts per page
 *               example: 20
 *             total_pages:
 *               type: integer
 *               description: Total number of pages
 *               example: 3
 *             has_next_page:
 *               type: boolean
 *               description: Whether there is a next page
 *               example: true
 *             has_prev_page:
 *               type: boolean
 *               description: Whether there is a previous page
 *               example: false
 *           required:
 *             - total_count
 *             - page
 *             - page_size
 *             - total_pages
 *             - has_next_page
 *             - has_prev_page
 *       required:
 *         - data
 *         - pagination
 *     
 *     ContactTag:
 *       type: object
 *       required:
 *         - tag_id
 *         - tag_name
 *       properties:
 *         tag_id:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *           description: MongoDB ObjectId of the tag
 *           example: "507f1f77bcf86cd799439011"
 *         tag_name:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           description: Name of the tag
 *           example: "VIP Customer"
 *     
 *     ContactCreateRequest:
 *       type: object
 *       required:
 *         - name
 *         - email
 *         - phone
 *       properties:
 *         name:
 *           type: string
 *           minLength: 1
 *           maxLength: 255
 *           description: Full name of the contact
 *           example: "John Doe"
 *         email:
 *           type: string
 *           format: email
 *           description: Email address of the contact
 *           example: "<EMAIL>"
 *         phone:
 *           type: string
 *           pattern: '^[+]?[0-9\s\-\(\)]+$'
 *           minLength: 7
 *           maxLength: 20
 *           description: Phone number of the contact
 *           example: "******-123-4567"
 *         country:
 *           type: string
 *           maxLength: 100
 *           description: Country name
 *           example: "United States"
 *         country_code:
 *           type: string
 *           pattern: '^[A-Z]{2}$'
 *           description: ISO 3166-1 alpha-2 country code
 *           example: "US"
 *         avatar_url:
 *           type: string
 *           format: uri
 *           description: URL to the contact's avatar image
 *           example: "https://example.com/avatar.jpg"
 *         tags:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ContactTag'
 *           description: Array of tags associated with the contact
 *         additional_fields:
 *           type: object
 *           additionalProperties:
 *             oneOf:
 *               - type: string
 *               - type: number
 *               - type: boolean
 *           description: Additional custom fields for the contact
 *           example:
 *             company: "Acme Corp"
 *             age: 30
 *             is_premium: true
 *         status:
 *           type: string
 *           enum: [active, archived, deleted, forgotten]
 *           default: active
 *           description: Status of the contact
 *     
 *     ContactResponse:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier of the contact
 *           example: "507f1f77bcf86cd799439011"
 *         org_id:
 *           type: string
 *           description: Organization identifier
 *           example: "org_123456789"
 *         created_by:
 *           type: string
 *           description: User ID who created the contact
 *           example: "user_123456789"
 *         name:
 *           type: string
 *           description: Full name of the contact
 *           example: "John Doe"
 *         email:
 *           type: string
 *           format: email
 *           description: Email address of the contact
 *           example: "<EMAIL>"
 *         phone:
 *           type: string
 *           description: Phone number of the contact
 *           example: "******-123-4567"
 *         country:
 *           type: string
 *           description: Country name
 *           example: "United States"
 *         country_code:
 *           type: string
 *           description: ISO 3166-1 alpha-2 country code
 *           example: "US"
 *         avatar_url:
 *           type: string
 *           format: uri
 *           description: URL to the contact's avatar image
 *           example: "https://example.com/avatar.jpg"
 *         tags:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ContactTag'
 *           description: Array of tags associated with the contact
 *         additional_fields:
 *           type: object
 *           additionalProperties:
 *             oneOf:
 *               - type: string
 *               - type: number
 *               - type: boolean
 *           description: Additional custom fields for the contact
 *         status:
 *           type: string
 *           enum: [active, archived, deleted, forgotten]
 *           description: Status of the contact
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the contact was created
 *           example: "2023-12-01T10:30:00.000Z"
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the contact was last updated
 *           example: "2023-12-01T10:30:00.000Z"
 *     
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         error:
 *           type: string
 *           description: Human-readable error message
 *           example: "Validation failed"
 *         errorCode:
 *           type: string
 *           description: Application-specific error code
 *           example: "VALIDATION_ERROR"
 *         details:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 *                 description: Field name that failed validation
 *                 example: "email"
 *               message:
 *                 type: string
 *                 description: Specific validation error message
 *                 example: "Email must be a valid email address"
 *           description: Detailed validation errors (for validation failures)
 */

/**
 * @swagger
 * /contacts:
 *   get:
 *     summary: Get contacts with pagination, filtering, and search
 *     description: |
 *       Retrieves a paginated list of contacts with advanced filtering capabilities.
 *       Supports MongoDB query-based filtering, powerful search functionality for email, mobile number, and name fields, and flexible sorting options.
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     parameters:
 *       # Pagination Parameters (Required)
 *       - in: query
 *         name: page
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination (required)
 *       - in: query
 *         name: page_size
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of contacts per page (required, max 100)
 *       
 *       # Sorting Parameters
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           enum: [name, email, phone, created_at, updated_at]
 *           default: created_at
 *         description: Field to sort contacts by
 *       - in: query
 *         name: sort_direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort direction (ascending or descending)
 *       
 *       # Search Parameter
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           minLength: 1
 *         description: |
 *           Search term for name, email, or phone number.
 *           Performs case-insensitive search with name matches prioritized.
 *           Supports partial matching for email and phone.
 *       
 *       # MongoDB Query for Advanced Filtering
 *       - in: query
 *         name: contactFilterQuery
 *         schema:
 *           type: string
 *         description: |
 *           JSON string of MongoDB query for advanced filtering.
 *           Supports all MongoDB query operators and complex nested conditions.
 *           
 *           Example structure:
 *           ```json
 *           {
 *             "$and": [
 *               {
 *                 "firstName": {
 *                   "$regex": "^Stev"
 *                 }
 *               },
 *               {
 *                 "lastName": {
 *                   "$in": ["Vai", "Vaughan"]
 *                 }
 *               },
 *               {
 *                 "age": {
 *                   "$gt": "28"
 *                 }
 *               },
 *               {
 *                 "$or": [
 *                   {
 *                     "isMusician": true
 *                   },
 *                   {
 *                     "instrument": "Guitar"
 *                   }
 *                 ]
 *               },
 *               {
 *                 "$eq": ["$groupedField1", "$groupedField4"]
 *               },
 *               {
 *                 "birthdate": {
 *                   "$gte": "1954-10-03",
 *                   "$lte": "1960-06-06"
 *                 }
 *               }
 *             ]
 *           }
 *           ```
 *           
 *           Commonly used MongoDB operators:
 *           - $eq: Equals
 *           - $ne: Not equal
 *           - $gt: Greater than
 *           - $lt: Less than
 *           - $gte: Greater than or equal
 *           - $lte: Less than or equal
 *           - $in: Value is in array
 *           - $nin: Value is not in array
 *           - $regex: Regular expression match
 *           - $exists: Field exists check
 *           - $and: Logical AND
 *           - $or: Logical OR
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginatedContactsResponse'
 *             examples:
 *               basic_pagination:
 *                 summary: Basic pagination example
 *                 value:
 *                   data:
 *                     - _id: "507f1f77bcf86cd799439013"
 *                       org_id: "org_123456789"
 *                       created_by: "user_123456789"
 *                       name: "John Doe"
 *                       email: "<EMAIL>"
 *                       phone: "******-123-4567"
 *                       country: "United States"
 *                       country_code: "US"
 *                       avatar_url: "https://example.com/avatar.jpg"
 *                       tags:
 *                         - tag_id: "507f1f77bcf86cd799439011"
 *                           tag_name: "VIP Customer"
 *                       additional_fields:
 *                         company: "Acme Corp"
 *                         age: 30
 *                         is_premium: true
 *                       status: "active"
 *                       created_at: "2023-12-01T10:30:00.000Z"
 *                       updated_at: "2023-12-01T10:30:00.000Z"
 *                     - _id: "507f1f77bcf86cd799439014"
 *                       org_id: "org_123456789"
 *                       created_by: "user_123456789"
 *                       name: "Jane Smith"
 *                       email: "<EMAIL>"
 *                       phone: "******-987-6543"
 *                       status: "active"
 *                       tags: []
 *                       additional_fields: {}
 *                       created_at: "2023-12-02T14:45:00.000Z"
 *                       updated_at: "2023-12-02T14:45:00.000Z"
 *                   pagination:
 *                     total_count: 42
 *                     page: 1
 *                     page_size: 20
 *                     total_pages: 3
 *                     has_next_page: true
 *                     has_prev_page: false
 *               filtered_search:
 *                 summary: Filtered search example
 *                 value:
 *                   data:
 *                     - _id: "507f1f77bcf86cd799439013"
 *                       org_id: "org_123456789"
 *                       created_by: "user_123456789"
 *                       name: "John Doe"
 *                       email: "<EMAIL>"
 *                       phone: "******-123-4567"
 *                       country: "United States"
 *                       country_code: "US"
 *                       tags:
 *                         - tag_id: "507f1f77bcf86cd799439011"
 *                           tag_name: "VIP Customer"
 *                       additional_fields:
 *                         company: "Acme Corp"
 *                       status: "active"
 *                       created_at: "2023-12-01T10:30:00.000Z"
 *                       updated_at: "2023-12-01T10:30:00.000Z"
 *                   pagination:
 *                     total_count: 1
 *                     page: 1
 *                     page_size: 20
 *                     total_pages: 1
 *                     has_next_page: false
 *                     has_prev_page: false
 *       400:
 *         description: Invalid query parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               invalid_pagination:
 *                 summary: Invalid pagination parameters
 *                 value:
 *                   error: "Invalid query parameters"
 *                   errorCode: "VALIDATION_ERROR"
 *                   details:
 *                     - field: "page"
 *                       message: "Page must be at least 1"
 *                     - field: "page_size"
 *                       message: "Page size cannot exceed 100"
 *               invalid_filter:
 *                 summary: Invalid filter JSON
 *                 value:
 *                   error: "Invalid query parameters"
 *                   errorCode: "VALIDATION_ERROR"
 *                   details:
 *                     - field: "filter"
 *                       message: "Filter must be a valid JSON string"
 *               invalid_react_query:
 *                 summary: Invalid react-query parameters
 *                 value:
 *                   error: "Invalid query parameters"
 *                   errorCode: "VALIDATION_ERROR"
 *                   details:
 *                     - field: "filterField"
 *                       message: "Filter field is required when filter value is provided"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid or expired authentication token"
 *               errorCode: "AUTH_003"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Internal server error"
 *               errorCode: "INTERNAL_001"
 */
router.get('/', supabaseAuthMiddleware, ContactsValidator.validateGetContactsMiddleware,
    async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        try {
            await ContactsHandler.getContacts(req, res);
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /contacts:
 *   post:
 *     summary: Create a new contact
 *     description: Creates a new contact within the authenticated user's organization. The contact will be associated with the user's organization automatically based on their authentication context.
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ContactCreateRequest'
 *           examples:
 *             basic_contact:
 *               summary: Basic contact with required fields only
 *               value:
 *                 name: "Jane Smith"
 *                 email: "<EMAIL>"
 *                 phone: "******-987-6543"
 *             full_contact:
 *               summary: Complete contact with all optional fields
 *               value:
 *                 name: "John Doe"
 *                 email: "<EMAIL>"
 *                 phone: "******-123-4567"
 *                 country: "United States"
 *                 country_code: "US"
 *                 avatar_url: "https://example.com/avatar.jpg"
 *                 tags:
 *                   - tag_id: "507f1f77bcf86cd799439011"
 *                     tag_name: "VIP Customer"
 *                   - tag_id: "507f1f77bcf86cd799439012"
 *                     tag_name: "Newsletter Subscriber"
 *                 additional_fields:
 *                   company: "Acme Corp"
 *                   department: "Engineering"
 *                   age: 30
 *                   is_premium: true
 *                 status: "active"
 *     responses:
 *       201:
 *         description: Contact created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ContactResponse'
 *             example:
 *               _id: "507f1f77bcf86cd799439013"
 *               org_id: "org_123456789"
 *               created_by: "user_123456789"
 *               name: "John Doe"
 *               email: "<EMAIL>"
 *               phone: "******-123-4567"
 *               country: "United States"
 *               country_code: "US"
 *               avatar_url: "https://example.com/avatar.jpg"
 *               tags:
 *                 - tag_id: "507f1f77bcf86cd799439011"
 *                   tag_name: "VIP Customer"
 *               additional_fields:
 *                 company: "Acme Corp"
 *                 age: 30
 *                 is_premium: true
 *               status: "active"
 *               created_at: "2023-12-01T10:30:00.000Z"
 *               updated_at: "2023-12-01T10:30:00.000Z"
 *       400:
 *         description: Invalid request data or missing organization context
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               validation_error:
 *                 summary: Validation error with field details
 *                 value:
 *                   error: "Validation failed"
 *                   errorCode: "VALIDATION_ERROR"
 *                   details:
 *                     - field: "email"
 *                       message: "Email must be a valid email address"
 *                     - field: "phone"
 *                       message: "Phone number is required"
 *               missing_organization:
 *                 summary: Missing organization context
 *                 value:
 *                   error: "Organization context is required"
 *                   errorCode: "ORG_001"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid or expired authentication token"
 *               errorCode: "AUTH_003"
 *       409:
 *         description: Conflict - Contact with the same email already exists in the organization
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Contact <NAME_EMAIL> already exists in this organization"
 *               errorCode: "CONTACT_001"
 *       422:
 *         description: Unprocessable Entity - Business logic validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Validation failed: Invalid tag reference"
 *               errorCode: "CONTACT_002"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Internal server error"
 *               errorCode: "INTERNAL_001"
 */
router.post('/', supabaseAuthMiddleware, ContactsValidator.validateCreateContactMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        try {
            await ContactsHandler.createContact(req, res);
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /contacts/{id}:
 *   get:
 *     summary: Get a contact by ID
 *     description: Retrieves a specific contact by its ID. The contact must belong to the authenticated user's organization.
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *         description: MongoDB ObjectId of the contact to retrieve
 *     responses:
 *       200:
 *         description: Contact retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ContactResponse'
 *       400:
 *         description: Invalid contact ID format
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid contact ID format"
 *               errorCode: "CONTACT_002"
 *               details:
 *                 - field: "id"
 *                   message: "Contact ID must be a valid MongoDB ObjectId (24 hexadecimal characters)"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid or expired authentication token"
 *               errorCode: "AUTH_003"
 *       404:
 *         description: Contact not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Contact with ID 507f1f77bcf86cd799439011 not found in organization org_123456789"
 *               errorCode: "CONTACT_003"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Internal server error"
 *               errorCode: "INTERNAL_001"
 */
router.get('/:id', supabaseAuthMiddleware, ContactsValidator.validateGetContactByIdMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        try {
            await ContactsHandler.getContactById(req, res);
        } catch (error) {
            next(error);
        }
    }
);

/**
 * @swagger
 * /contacts/csv-headers:
 *   post:
 *     summary: Extract headers from CSV/Excel file
 *     description: Uploads a CSV or Excel file and extracts headers for validation against contact schema
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: CSV or Excel file to upload
 *     responses:
 *       200:
 *         description: Headers extracted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     csvHeaders:
 *                       type: array
 *                       items:
 *                         type: string
 *                     contactSchema:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           type:
 *                             type: string
 *                           required:
 *                             type: boolean
 *                           description:
 *                             type: string
 *                     fileInfo:
 *                       type: object
 *                       properties:
 *                         name:
 *                           type: string
 *                         size:
 *                           type: number
 *                         rowCount:
 *                           type: number
 *       400:
 *         description: Invalid file or validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/csv-headers',
    supabaseAuthMiddleware,
    FileValidator.getUploadMiddleware(),
    FileValidator.handleErrors,
    FileValidator.validateFilePresence,
    ContactsHandler.extractCSVHeaders
);

/**
 * @swagger
 * /contacts/bulk-import:
 *   post:
 *     summary: Bulk import contacts from CSV/Excel file
 *     description: |
 *       Uploads a CSV or Excel file with contact data and creates a background job to import the contacts.
 *       The job processes the file asynchronously and sends a notification when complete.
 *     tags:
 *       - Contacts
 *     security:
 *       - Token: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: CSV or Excel file containing contact data
 *               fieldMappings:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     fileColumnName:
 *                       type: string
 *                       description: Column name in the CSV/Excel file
 *                     systemAttributeName:
 *                       type: string
 *                       description: Corresponding field name in the contact schema
 *                 description: Mapping between file columns and contact schema fields
 *             required:
 *               - file
 *               - fieldMappings
 *     responses:
 *       202:
 *         description: Bulk import job created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Bulk import job created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     jobId:
 *                       type: string
 *                       description: ID of the created bulk import job
 *                       example: "507f1f77bcf86cd799439011"
 *                     status:
 *                       type: string
 *                       enum: [pending, processing, completed, failed]
 *                       description: Current status of the job
 *                       example: "pending"
 *                     fileName:
 *                       type: string
 *                       description: Name of the uploaded file
 *                       example: "contacts.csv"
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       description: Timestamp when the job was created
 *                       example: "2023-12-01T10:30:00.000Z"
 *       400:
 *         description: Invalid request - file validation or field mapping errors
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               file_missing:
 *                 summary: No file uploaded
 *                 value:
 *                   error: "No file uploaded"
 *                   errorCode: "BULK_IMPORT_001"
 *                   details:
 *                     - field: "file"
 *                       message: "File is required for bulk import"
 *               invalid_file:
 *                 summary: Invalid file format
 *                 value:
 *                   error: "File contains too many rows. Maximum is 10000"
 *                   errorCode: "BULK_IMPORT_002"
 *                   details:
 *                     - field: "file"
 *                       message: "File contains too many rows. Maximum is 10000"
 *               invalid_mapping:
 *                 summary: Invalid field mapping
 *                 value:
 *                   error: "Validation failed for contact bulk import"
 *                   errorCode: "CONTACT_BULK_IMPORT_VALIDATION_001"
 *                   details:
 *                     - field: "fieldMappings"
 *                       message: "Required field 'email' must be mapped"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Invalid or expired authentication token"
 *               errorCode: "AUTH_003"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Internal server error"
 *               errorCode: "INTERNAL_001"
 */
router.post('/bulk-import',
    supabaseAuthMiddleware,
    FileValidator.getUploadMiddleware(),
    FileValidator.handleErrors,
    FileValidator.validateFilePresence,
    ContactsValidator.validateBulkImportMiddleware,
    ContactsHandler.bulkImportContacts
);

export default router;
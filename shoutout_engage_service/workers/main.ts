import { parentPort } from 'worker_threads';
import { logger } from '../lib/logger';
import config from '../lib/config';
import { ContactBulkImportProcessor } from './processors/contact.bulk.import.processor';
import { EmailProcessor } from './processors/email.processor';

const log = logger(config.logger);

// Worker state
let isRunning = true;
let heartbeatInterval: NodeJS.Timeout | null = null;

// Worker thread main function
async function main() {
    try {
        log.info('Worker thread started');

        // Send a message to the main thread
        if (parentPort) {
            parentPort.postMessage({ type: 'init', message: 'Worker initialized successfully' });
        }

        // Start the contact bulk import processor
        log.info('Starting contact bulk import processor');
        ContactBulkImportProcessor.startProcess();
        log.info('Contact bulk import processor started');
        
        // Start the email processor
        log.info('Starting email processor');
        EmailProcessor.startProcess();
        log.info('Email processor started');

        // Start heartbeat only if worker is still running
        if (isRunning) {
            heartbeatInterval = setInterval(() => {
                if (isRunning && parentPort) {
                    parentPort.postMessage({ type: 'heartbeat', timestamp: new Date().toISOString() });
                }
            }, 60000); // Every 60 seconds
        }

    } catch (error) {
        log.error('Worker initialization error:', error);
        if (parentPort) {
            parentPort.postMessage({ type: 'error', error: error instanceof Error ? error.message : String(error) });
        }
        process.exit(1);
    }
}

// Handle messages from main thread
if (parentPort) {
    parentPort.on('message', (message) => {
        console.log('Worker received message:', message);

        // Handle shutdown message
        if (message.type === 'shutdown') {
            shutdown();
        }
    });
}

// Graceful shutdown function
function shutdown() {
    console.log('Worker shutting down...');
    isRunning = false;

    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
    }

    if (parentPort) {
        parentPort.postMessage({ type: 'shutdown', message: 'Worker shutdown complete' });
    }

    process.exit(0);
}

// Handle process termination
process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

// Start the worker
main().catch((error) => {
    console.error('Worker error:', error);
    process.exit(1);
});
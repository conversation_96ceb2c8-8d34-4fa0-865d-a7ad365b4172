import { Job } from 'bull';
import { contactBulkImportQueue, contactBulkImportProcessorQueue } from '../../lib/queue/queue';
import { logger } from '../../lib/logger';
import config from '../../lib/config';
import { ContactDAO } from '../../lib/db/dao/ContactDAO';
import { CSVService } from '../../services/csv.service';
import { BULK_IMPORT_STATUS, BULK_IMPORT_ERROR_TYPE, BulkImportError, FieldMapping } from '../../lib/constant/queue.constants';
import { Readable } from 'stream';
import csvParser from 'csv-parser';
import { ObjectId } from 'mongodb';
import { EmailProcessor } from './email.processor';
import { EmailTemplate } from '../../lib/utils/template.loader';

const log = logger(config.logger);

// Batch size for processing contacts
const BATCH_SIZE = 100;

/**
 * Interface for contact bulk import job data
 */
interface ContactBulkImportJobData {
  jobId: string;
  organizationId: string;
  userId: string;
  fileName: string;
  fieldMappings: FieldMapping[];
  fileBuffer: string; // Base64 encoded file buffer
}

/**
 * Interface for contact data from CSV
 */
interface ContactData {
  [key: string]: string;
}

/**
 * Processor for contact bulk import jobs
 */
export class ContactBulkImportProcessor {
  /**
   * Start processing contact bulk import jobs
   */
  static startProcess(): void {
    // Process bulk import jobs
    contactBulkImportQueue.process(async (job: Job<ContactBulkImportJobData>) => {
      try {
        log.info(`Starting contact bulk import job ${job.id}`);
        
        const { jobId, organizationId, userId, fileName, fieldMappings, fileBuffer } = job.data;
        
        // Update job status to processing
        await ContactDAO.updateBulkImportJob(
          {
            status: BULK_IMPORT_STATUS.PROCESSING,
            started_at: new Date()
          },
          jobId,
          organizationId,
          { status: BULK_IMPORT_STATUS.PENDING }
        );
        
        // Process the file
        await this.processFile(job);
        
        return { success: true };
      } catch (error: unknown) {
        log.error(`Error processing contact bulk import job ${job.id}:`, error);
        
        // Update job status to failed
        try {
          const { jobId, organizationId } = job.data;
          
          await ContactDAO.updateBulkImportJob(
            {
              status: BULK_IMPORT_STATUS.FAILED,
              completed_at: new Date()
            },
            jobId,
            organizationId
          );
          
          // Send failure email via queue
          const bulkImportJob = await ContactDAO.getBulkImportJobById(organizationId, jobId);
          
          const emailQueue = EmailProcessor.getQueue();
          await emailQueue.add({
            to: job.data.userId,
            template: EmailTemplate.BULK_IMPORT_COMPLETION,
            templateData: {
              jobId,
              fileName: job.data.fileName,
              status: BULK_IMPORT_STATUS.FAILED,
              stats: {
                totalRows: bulkImportJob.total_rows,
                processedRows: bulkImportJob.processed_rows,
                successfulRows: bulkImportJob.successful_rows,
                failedRows: bulkImportJob.failed_rows
              },
              errors: bulkImportJob.errors
            }
          });
        } catch (emailError) {
          log.error(`Error queuing failure email for job ${job.id}:`, emailError);
        }
        
        throw error;
      }
    });
    
    // Process individual contacts from the processor queue
    contactBulkImportProcessorQueue.process(BATCH_SIZE, async (job: Job<any>) => {
      try {
        log.debug(`Processing contact from bulk import job ${job.data.jobId}, row ${job.data.rowIndex}`);
        
        const { jobId, organizationId, contactData, rowIndex, fieldMappings } = job.data;
        
        // Map CSV data to contact fields
        const mappedContact = this.mapContactData(contactData, fieldMappings);
        
        try {
          // Create the contact
          await ContactDAO.createContact({
            org_id: organizationId,
            created_by: job.data.userId,
            ...mappedContact
          });
          
          // Increment successful rows count
          await ContactDAO.incrementSuccessfulRows(jobId, organizationId);
          
          return { success: true };
        } catch (error: unknown) {
          // Add error to job
          const bulkImportError: BulkImportError = {
            row: rowIndex,
            type: this.determineErrorType(error),
            message: error instanceof Error ? error.message : 'Unknown error',
            field: this.determineErrorField(error),
            value: this.determineErrorValue(error, contactData)
          };
          
          await ContactDAO.addErrorToBulkImportJob(jobId, organizationId, bulkImportError);
          
          // Don't throw the error, just log it and continue processing
          log.error(`Error processing contact at row ${rowIndex}:`, error);
          return { success: false, error: bulkImportError };
        }
      } catch (error: unknown) {
        log.error(`Error in contact processor for job ${job.data.jobId}:`, error);
        throw error;
      }
    });
  }
  
  /**
   * Process the CSV file
   * @param job Bull job with contact bulk import data
   */
  private static async processFile(job: Job<ContactBulkImportJobData>): Promise<void> {
    const { jobId, organizationId, userId, fileName, fieldMappings, fileBuffer } = job.data;
    
    // Convert base64 buffer back to Buffer
    const buffer = Buffer.from(fileBuffer, 'base64');
    
    // Create readable stream from buffer
    const stream = Readable.from(buffer);
    
    // Track job progress
    let totalRows = 0;
    let processedRows = 0;
    let rowIndex = 0;
    
    // Create a promise that resolves when the stream is fully processed
    return new Promise((resolve, reject) => {
      stream
        .pipe(csvParser())
        .on('data', async (data: ContactData) => {
          rowIndex++;
          totalRows++;
          
          try {
            // Add job to processor queue
            await contactBulkImportProcessorQueue.add({
              jobId,
              organizationId,
              userId,
              contactData: data,
              rowIndex,
              fieldMappings
            });
            
            processedRows++;
            
            // Update progress every 100 rows
            if (processedRows % 100 === 0) {
              job.progress(Math.floor((processedRows / totalRows) * 100));
              
              // Update total rows count in the job
              await ContactDAO.updateBulkImportJob(
                { total_rows: totalRows },
                jobId,
                organizationId
              );
            }
          } catch (error) {
            log.error(`Error adding contact to processor queue at row ${rowIndex}:`, error);
            
            // Add error to job
            const bulkImportError: BulkImportError = {
              row: rowIndex,
              type: BULK_IMPORT_ERROR_TYPE.SYSTEM,
              message: error instanceof Error ? error.message : 'Unknown error',
              field: 'system',
              value: 'queue'
            };
            
            await ContactDAO.addErrorToBulkImportJob(jobId, organizationId, bulkImportError);
          }
        })
        .on('end', async () => {
          try {
            // Update job with final counts and status
            await ContactDAO.updateBulkImportJob(
              {
                total_rows: totalRows,
                status: BULK_IMPORT_STATUS.COMPLETED,
                completed_at: new Date()
              },
              jobId,
              organizationId
            );
            
            // Get the updated job
            const bulkImportJob = await ContactDAO.getBulkImportJobById(organizationId, jobId);
            
            // Get email queue
            const emailQueue = EmailProcessor.getQueue();
            
            // Send completion email via queue
            await emailQueue.add({
              to: userId,
              template: EmailTemplate.BULK_IMPORT_COMPLETION,
              templateData: {
                jobId,
                fileName,
                status: BULK_IMPORT_STATUS.COMPLETED,
                stats: {
                  totalRows: bulkImportJob.total_rows,
                  processedRows: bulkImportJob.processed_rows,
                  successfulRows: bulkImportJob.successful_rows,
                  failedRows: bulkImportJob.failed_rows
                },
                errors: bulkImportJob.errors
              }
            });
            
            // If there are errors, send detailed error report via queue
            if (bulkImportJob.errors.length > 0) {
              await emailQueue.add({
                to: userId,
                template: EmailTemplate.BULK_IMPORT_ERROR_REPORT,
                templateData: {
                  jobId,
                  fileName,
                  errors: bulkImportJob.errors
                }
              });
            }
            
            job.progress(100);
            resolve();
          } catch (error) {
            log.error(`Error finalizing bulk import job ${jobId}:`, error);
            reject(error);
          }
        })
        .on('error', (error) => {
          log.error(`Error processing CSV file for job ${jobId}:`, error);
          reject(error);
        });
    });
  }
  
  /**
   * Map CSV data to contact fields based on field mappings
   * @param data CSV row data
   * @param fieldMappings Field mappings
   * @returns Mapped contact data
   */
  private static mapContactData(data: ContactData, fieldMappings: FieldMapping[]): any {
    const mappedContact: any = {};
    
    // Map fields based on field mappings
    fieldMappings.forEach(mapping => {
      const { fileColumnName, systemAttributeName } = mapping;
      
      if (data[fileColumnName] !== undefined) {
        mappedContact[systemAttributeName] = data[fileColumnName];
      }
    });
    
    // Handle special fields
    if (mappedContact.tags) {
      try {
        // If tags is a string, try to parse it as JSON
        if (typeof mappedContact.tags === 'string') {
          const parsedTags = JSON.parse(mappedContact.tags);
          
          // Convert to the expected format
          if (Array.isArray(parsedTags)) {
            mappedContact.tags = parsedTags.map(tag => {
              if (typeof tag === 'string') {
                return {
                  tag_id: new ObjectId(),
                  tag_name: tag
                };
              } else if (typeof tag === 'object' && tag.name) {
                return {
                  tag_id: new ObjectId(),
                  tag_name: tag.name
                };
              }
              return null;
            }).filter(Boolean);
          }
        }
      } catch (error) {
        // If parsing fails, treat it as a comma-separated list
        mappedContact.tags = mappedContact.tags.split(',')
          .map((tag: string) => tag.trim())
          .filter((tag: string) => tag)
          .map((tag: string) => ({
            tag_id: new ObjectId(),
            tag_name: tag
          }));
      }
    }
    
    // Handle additional_fields
    const additionalFields: Record<string, any> = {};
    Object.keys(data).forEach(key => {
      // If the field is not mapped to a standard field, add it to additional_fields
      if (!fieldMappings.some(mapping => mapping.fileColumnName === key)) {
        additionalFields[key] = data[key];
      }
    });
    
    if (Object.keys(additionalFields).length > 0) {
      mappedContact.additional_fields = additionalFields;
    }
    
    return mappedContact;
  }
  
  /**
   * Determine error type based on the error
   * @param error Error object
   * @returns Error type
   */
  private static determineErrorType(error: unknown): BULK_IMPORT_ERROR_TYPE {
    if (error instanceof Error) {
      if (error.name === 'ValidationError') {
        return BULK_IMPORT_ERROR_TYPE.VALIDATION;
      } else if (error.name === 'DuplicateError' || error.message.includes('duplicate')) {
        return BULK_IMPORT_ERROR_TYPE.DUPLICATE;
      } else if (error.name === 'DatabaseError') {
        return BULK_IMPORT_ERROR_TYPE.DATABASE;
      }
    }
    return BULK_IMPORT_ERROR_TYPE.SYSTEM;
  }
  
  /**
   * Determine error field based on the error
   * @param error Error object
   * @returns Error field
   */
  private static determineErrorField(error: unknown): string | undefined {
    if (error instanceof Error) {
      // Check for validation errors with details
      if (error.name === 'ValidationError' && 'details' in error && Array.isArray((error as any).details)) {
        const details = (error as any).details;
        if (details.length > 0 && details[0].field) {
          return details[0].field;
        }
      }
      
      // Check for duplicate errors
      if (error.name === 'DuplicateError') {
        if (error.message.includes('email')) {
          return 'email';
        } else if (error.message.includes('phone')) {
          return 'phone';
        }
      }
    }
    return undefined;
  }
  
  /**
   * Determine error value based on the error and contact data
   * @param error Error object
   * @param contactData Contact data
   * @returns Error value
   */
  private static determineErrorValue(error: unknown, contactData: ContactData): string | undefined {
    const field = this.determineErrorField(error);
    
    if (field && contactData[field]) {
      return contactData[field];
    }
    
    return undefined;
  }
  
  /**
   * Get the contact bulk import queue
   * @returns Bull queue
   */
  static getQueue() {
    return contactBulkImportQueue;
  }
  
  /**
   * Get the contact bulk import processor queue
   * @returns Bull queue
   */
  static getProcessorQueue() {
    return contactBulkImportProcessorQueue;
  }
}
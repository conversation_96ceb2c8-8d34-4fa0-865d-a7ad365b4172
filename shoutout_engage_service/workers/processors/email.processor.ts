import { Job } from 'bull';
import { emailQueue } from '../../lib/queue/queue';
import { logger } from '../../lib/logger';
import config from '../../lib/config';
import { SupabaseEmailService } from '../../services/implementations/supabase.email.service';
import { EmailTemplate } from '../../lib/utils/template.loader';
import { 
  EmailData, 
  EmailTemplateData,
  BulkImportCompletionTemplateData,
  BulkImportErrorReportTemplateData
} from '../../types/email.types';

const log = logger(config.logger);

/**
 * Email job data interface
 */
export interface EmailJobData {
  // Basic email data (used when template is not specified)
  to: string;
  subject?: string;
  html?: string;
  
  // Template data (used when template is specified)
  template?: EmailTemplate;
  templateData?: EmailTemplateData;
}

/**
 * Email processor for sending emails from the queue
 */
export class EmailProcessor {
  /**
   * Start processing email jobs from the queue
   */
  static startProcess(): void {
    emailQueue.process(async (job: Job<EmailJobData>) => {
      try {
        log.info(`Processing email job ${job.id}`);
        
        const { to, subject, html, template, templateData } = job.data;
        
        // Create email service instance
        const emailService = new SupabaseEmailService();
        
        // Send email based on whether a template is specified
        let success = false;
        
        if (template && templateData) {
          // Send email using template
          log.info(`Sending template email (${template}) to ${to}`);
          
          // Validate template data based on template type
          this.validateTemplateData(template, templateData);
          
          // Send email using template
          success = await emailService.sendTemplate(to, template, templateData);
        } else if (subject && html) {
          // Send email directly
          log.info(`Sending direct email to ${to}`);
          
          const emailData: EmailData = {
            to,
            subject,
            html
          };
          
          success = await emailService.send(emailData);
        } else {
          throw new Error('Invalid email job data: must provide either template+templateData or subject+html');
        }
        
        if (success) {
          log.info(`Email job ${job.id} completed successfully`);
          return { success: true };
        } else {
          throw new Error('Failed to send email');
        }
      } catch (error: unknown) {
        log.error(`Error processing email job ${job.id}:`, error);
        throw error;
      }
    });
    
    log.info('Email processor started');
  }
  
  /**
   * Validate template data based on template type
   * @param template Email template
   * @param templateData Template data
   * @throws Error if template data is invalid
   */
  private static validateTemplateData(template: EmailTemplate, templateData: EmailTemplateData): void {
    switch (template) {
      case EmailTemplate.BULK_IMPORT_COMPLETION:
        const completionData = templateData as BulkImportCompletionTemplateData;
        
        if (!completionData.jobId || !completionData.fileName || !completionData.status || !completionData.stats) {
          throw new Error('Invalid bulk import completion template data: missing required fields');
        }
        
        if (!completionData.stats.totalRows && completionData.stats.totalRows !== 0) {
          throw new Error('Invalid bulk import completion template data: missing stats.totalRows');
        }
        
        break;
        
      case EmailTemplate.BULK_IMPORT_ERROR_REPORT:
        const errorReportData = templateData as BulkImportErrorReportTemplateData;
        
        if (!errorReportData.jobId || !errorReportData.fileName || !errorReportData.errors) {
          throw new Error('Invalid bulk import error report template data: missing required fields');
        }
        
        break;
        
      default:
        throw new Error(`Unknown template: ${template}`);
    }
  }
  
  /**
   * Get the email queue
   * @returns Bull queue
   */
  static getQueue() {
    return emailQueue;
  }
}
{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./dist", "rootDir": "./", "baseUrl": "./"}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node", "transpileOnly": true, "files": true}}
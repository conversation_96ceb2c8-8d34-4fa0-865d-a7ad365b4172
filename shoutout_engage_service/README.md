# ShoutOUT Engage Service

Backend service for ShoutOUT Engage with Supabase JWT authentication, built with TypeScript.

## Project Structure

The backend follows a modular architecture with folders like:

- **src/routes**: for RESTful API route definitions
- **src/controllers**: to handle incoming API requests
- **src/services**: for business logic
- **src/middleware**: for request middleware, including Supabase JWT auth
- **src/config**: for loading Redis, MongoDB, and Supabase clients
- **src/types**: for TypeScript type definitions
- **src/app.ts and server.ts**: the main application and entry point

### Types Folder Structure

The `src/types` folder contains all TypeScript type definitions:

- **auth.ts**: User, Session, and authentication-related types
- **campaign.ts**: Campaign and campaign-related types
- **api.ts**: API request and response types
- **database.ts**: Supabase database schema types
- **index.ts**: Exports all types for easy importing

## Authentication Middleware

The project includes a Supabase JWT authentication middleware that:

- Extracts the JWT from the Authorization header
- Verifies it using the Supabase client
- Rejects unauthorized requests
- Attaches the verified user to `req.user`

### Usage

```typescript
import { authenticateRequest } from '@/middleware/authMiddleware';
import { Router } from 'express';

const router: Router = express.Router();

// Protect a route with authentication
router.get('/campaigns', authenticateRequest, getCampaigns);
```

## Environment Variables

Create a `.env` file based on `.env.example` with the following variables:

```
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Installation

```bash
# Clone the repository
git clone <repository-url>

# Install dependencies
npm install

# Create .env file
cp .env.example .env
# Then edit .env with your Supabase credentials

# Build the TypeScript code
npm run build

# Start the server
npm start

# Start in development mode with auto-reload (TypeScript compilation on-the-fly)
npm run dev
```

## TypeScript Configuration

This project uses TypeScript for type safety and better developer experience. The TypeScript configuration is defined in `tsconfig.json`:

- Target: ES2020
- Module: NodeNext
- Strict type checking enabled
- Source maps for debugging
- Output directory: `dist`
- Path aliases: `@/` for `src/` directory

TypeScript provides several benefits:
- Static type checking
- Better IDE support with autocompletion
- Early error detection
- Self-documenting code
- Improved refactoring capabilities

## API Endpoints

- `GET /health`: Health check endpoint
- `GET /api`: API welcome message
- `GET /api/campaigns`: Get all campaigns (protected)
- `GET /api/campaigns/:id`: Get a specific campaign (protected)

## License

[MIT](LICENSE)
import { ObjectId } from 'mongodb';
import { ERROR_REPORT_TYPE } from './error.types';

// ========================================
// CREATE CONTACT TYPES (First Priority)
// ========================================

export enum ContactStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted'
}

export interface ContactTag {
  tag_id: ObjectId;
  tag_name: string;
}

/**
 * Core contact entity stored in database
 */
export interface Contact {
  _id: ObjectId;
  org_id: string;
  created_by: string;
  name: string;
  email: string;
  phone: string;
  country?: string;
  country_code?: string;
  avatar_url?: string;
  tags: ContactTag[];
  additional_fields: {
    [key: string]: string | number | boolean;
  };
  status: ContactStatus;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface for incoming HTTP request body (tags have string IDs, not ObjectIds)
 */
export interface ContactCreateRequest {
  name: string;
  email: string;
  phone: string;
  country?: string;
  country_code?: string;
  avatar_url?: string;
  tags?: Array<{
    tag_id: string; // String in request, converted to ObjectId internally
    tag_name: string;
  }>;
  additional_fields?: {
    [key: string]: string | number | boolean;
  };
  status?: ContactStatus;
}

/**
 * Define CreateContactData interface based on Contact but omit database-specific fields
 */
export interface CreateContactData extends Omit<Contact, '_id' | 'created_at' | 'updated_at'> {
  // All fields from Contact except _id, created_at, updated_at
}

// ========================================
// GET/QUERY CONTACT TYPES (Second Priority)
// ========================================

/**
 * Interface for query parameters used in GET /contacts endpoint
 */
export interface ContactsQueryParams {
  page: number;         // Required parameter
  page_size: number;    // Required parameter, Max: 100
  sort_by?: string;      // Default: 'created_at'
  sort_direction?: 'asc' | 'desc'; // Default: 'desc'
  search?: string;
  contactFilterQuery?: string;  // JSON string of MongoDB query
}

/**
 * Contact response format for API (ObjectIds converted to strings)
 */
export interface ContactResponse {
  _id: string;
  org_id: string;
  created_by: string;
  name: string;
  email: string;
  phone: string;
  country?: string;
  country_code?: string;
  avatar_url?: string;
  tags: Array<{
    tag_id: string;
    tag_name: string;
  }>;
  additional_fields: {
    [key: string]: string | number | boolean;
  };
  status: ContactStatus;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface for paginated contacts response
 * Used for GET /contacts endpoint
 */
export interface PaginatedContactsResponse {
  data: ContactResponse[];
  pagination: {
    total_count: number;
    page: number;
    page_size: number;
    total_pages: number;
    has_next_page: boolean;
    has_prev_page: boolean;
  };
}

/**
 * Supported operators for filter rules
 */
export type FilterOperator = 
  | 'equals' 
  | 'not_equals' 
  | 'contains' 
  | 'not_contains' 
  | 'greater' 
  | 'less' 
  | 'greater_or_equal' 
  | 'less_or_equal' 
  | 'in' 
  | 'not_in' 
  | 'is_null' 
  | 'is_not_null';

/**
 * Interface for filter rules used in advanced filtering
 * Compatible with react-querybuilder's output format
 */
export interface Rule {
  field: string;
  operator: FilterOperator;
  value: string | number | boolean | Array<string | number | boolean> | null;
}

/**
 * Interface for advanced filtering with react-querybuilder compatibility
 * Supports nested filter structures with combinators (AND/OR)
 */
export interface FilterQuery {
  combinator: 'and' | 'or';
  rules: Array<Rule | FilterQuery>;
}

// ========================================
// CSV CONTACT TYPES (Third Priority)
// ========================================

/**
 * Interface for contact schema field information
 * Used for CSV header mapping
 */
export interface ContactSchemaField {
  name: string;
  type: string;
  required: boolean;
  description?: string;
}

// ========================================
// BULK IMPORT TYPES (Fourth Priority)
// ========================================

/**
 * Contact bulk import job field mapping
 * Maps CSV column names to contact schema fields
 */
export interface FieldMapping {
  fileColumnName: string;
  systemAttributeName: string;
}

/**
 * Contact bulk import job status
 */
export enum BULK_IMPORT_STATUS {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

/**
 * Contact bulk import job error types
 */
export enum BULK_IMPORT_ERROR_TYPE {
  VALIDATION = 'validation',
  DUPLICATE = 'duplicate',
  DATABASE = 'database',
  SYSTEM = 'system'
}

/**
 * Contact bulk import job error
 */
export interface BulkImportError {
  row: number;
  type: BULK_IMPORT_ERROR_TYPE;
  message: string;
  field?: string;
  value?: string;
}

/**
 * Contact bulk import job result
 */
export interface BulkImportResult {
  totalRows: number;
  processedRows: number;
  successfulRows: number;
  failedRows: number;
  errors: BulkImportError[];
}
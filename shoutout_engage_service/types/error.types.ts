/**
 * Error-related type definitions
 */

/**
 * Error report types
 */
export enum ERROR_REPORT_TYPE {
    CSV = 'csv',
    JSON = 'json',
    EMAIL = 'email'
}

/**
 * Generic error detail interface
 */
export interface ErrorDetail {
    field: string;
    message: string;
}

/**
 * Generic error response interface
 */
export interface ErrorResponse {
    error: string;
    errorCode: string;
    details?: ErrorDetail[];
}
import { EmailTemplate } from '../lib/utils/template.loader';
import { BulkImportError, BULK_IMPORT_STATUS } from '../lib/constant/queue.constants';

/**
 * Basic email data interface
 */
export interface EmailData {
  to: string;
  subject: string;
  html: string;
}

/**
 * Bulk import completion email template data
 */
export interface BulkImportCompletionTemplateData {
  jobId: string;
  fileName: string;
  status: BULK_IMPORT_STATUS;
  stats: {
    totalRows: number;
    processedRows: number;
    successfulRows: number;
    failedRows: number;
  };
  errors: BulkImportError[];
}

/**
 * Bulk import error report email template data
 */
export interface BulkImportErrorReportTemplateData {
  jobId: string;
  fileName: string;
  errors: BulkImportError[];
}

/**
 * Union type for all template data types
 */
export type EmailTemplateData = 
  | BulkImportCompletionTemplateData 
  | BulkImportErrorReportTemplateData;

/**
 * Email service interface
 */
export interface IEmailService {
  /**
   * Send an email
   * @param emailData Email data
   * @returns Promise resolving to success status
   */
  send(emailData: EmailData): Promise<boolean>;

  /**
   * Send an email using a template
   * @param to Recipient email address
   * @param template Email template to use
   * @param templateData Data to populate the template
   * @returns Promise resolving to success status
   */
  sendTemplate(
    to: string,
    template: EmailTemplate,
    templateData: EmailTemplateData
  ): Promise<boolean>;
}
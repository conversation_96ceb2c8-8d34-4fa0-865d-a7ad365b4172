import { ObjectId } from 'mongodb';

// Contact event type enum
export enum ContactEventType {
  MESSAGE_SENT = 'message_sent',
  OPENED = 'opened',
  CLICKED = 'clicked',
  FAILED = 'failed',
  IMPORTED = 'imported'
}

// Contact activity interface
export interface ContactActivity {
  _id: ObjectId;
  org_id: string;
  contact_id: ObjectId;
  event_type: ContactEventType;
  campaign_id?: string;
  campaign_name?: string;
  metadata?: Record<string, any>;
  created_at: Date;
}
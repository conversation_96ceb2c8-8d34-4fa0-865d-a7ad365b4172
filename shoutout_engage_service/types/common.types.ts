/**
 * Common types used across the application
 */

// Standard error response interface
export interface ErrorResponse {
    error: string;
    errorCode?: string;
    details?: Array<{
        field: string;
        message: string;
    }>;
}

// Standard success response wrapper
export interface SuccessResponse<T> {
    data: T;
    message?: string;
}

// Pagination metadata
export interface PaginationMeta {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
}

// Paginated response wrapper
export interface PaginatedResponse<T> {
    data: T[];
    meta: PaginationMeta;
}
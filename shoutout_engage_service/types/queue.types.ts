/**
 * Queue-related type definitions
 */

/**
 * Queue names for Bull MQ job queues
 */
export const QUEUES = {
    // Contact related queues
    CONTACT_BULK_IMPORT_QUEUE: 'contact_bulk_import_queue',
    CONTACT_BULK_IMPORT_PROCESSOR_QUEUE: 'contact_bulk_import_processor_queue',
    
    // Email related queues
    EMAIL_QUEUE: 'email_queue',
    
    // Add other queues as needed
};

/**
 * Default options for Bull MQ queues
 */
export const DEFAULT_QUEUE_OPTIONS = {
    attempts: 3,
    timeout: 24 * 60 * 60 * 1000, // 24 hours
    removeOnComplete: { count: 1000 },
    removeOnFail: { count: 1000 }
};

/**
 * Job status enum
 */
export enum JOB_STATUS {
    PENDING = 'pending',
    PROCESSING = 'processing',
    COMPLETED = 'completed',
    FAILED = 'failed'
}

/**
 * Job category enum
 */
export enum JOB_CATEGORY {
    SYSTEM = 'SYSTEM',
    CORE = 'CORE',
    CONTACT = 'CONTACT',
    UNLISTED = 'UNLISTED'
}
#!/usr/bin/env node

/**
 * Load environment variables first
 */
import * as dotenv from 'dotenv';
import * as path from 'path';
import { fileURLToPath } from 'url';

const wwwFilename = fileURLToPath(import.meta.url);
const wwwDirname = path.dirname(wwwFilename);

// Load environment variables before any other imports
dotenv.config({ path: path.resolve(wwwDirname, '../.env') });

/**
 * Module dependencies.
 */

import appPromise from '../app';
import debugLib from 'debug';
import config from '../lib/config';
import {logger} from '../lib/logger';

import { createTerminus } from '@godaddy/terminus';
import RedisConnector from '../lib/db/connectors/RedisConnector';
import MongooseConnector from '../lib/db/connectors/MongooseConnector';
import PrismaConnector from '../lib/db/connectors/PrismaConnector';
import { Worker, isMainThread } from 'worker_threads';

const debug = debugLib('engage:server');
const log = logger(config.logger);

if (isMainThread) {
    (async () => {
        const { app, server } = await appPromise();

        const port = normalizePort(process.env.PORT || '3001');
        app.set('port', port);

        const healthCheckPath = `${config.api.base_path}/healthcheck`;
        const healthChecks: Record<string, () => Promise<void>> = {
            [healthCheckPath]: onHealthCheck,
        };
       //gracefully shut down on termination signals
        createTerminus(server, {
            signal: 'SIGINT',
            healthChecks,
            onSignal,
        });

        server.listen(port);
        server.on('error', onError);
        server.on('listening', onListening);

        async function onSignal(): Promise<void> {
            log.info('server is starting cleanup');

            // Terminate worker thread first
            if (worker) {
                log.info('terminating worker thread...');
                try {
                    worker.postMessage({ type: 'shutdown' });
                    // Give worker time to shutdown gracefully
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    await worker.terminate();
                    log.info('worker thread terminated');
                } catch (error) {
                    log.error('error terminating worker:', error);
                }
            }

            log.info('cleaning redis cache...');
            log.info('cleaning user cache...');
            await RedisConnector.scanAndDelete('users:*');
            log.info('cleaning modules cache...');
            await RedisConnector.scanAndDelete('groups:*');
            log.info('cleaning groups cache...');
            await RedisConnector.scanAndDelete('modules:*');

            log.info('closing redis connection...');
            await RedisConnector.closeConnection();
            log.info('closing mongo connection...');
            await MongooseConnector.close();
            log.info('closing postgres connection...');
            await PrismaConnector.close();
        }

        async function onHealthCheck(): Promise<void> {
            log.debug(`health check on ${new Date().toISOString()}`);
            // Add actual health checks here if needed.
        }

        function normalizePort(val: string): number | string | false {
            const port = parseInt(val, 10);
            if (isNaN(port)) return val;
            if (port >= 0) return port;
            return false;
        }

        function onError(error: NodeJS.ErrnoException): void {
            if (error.syscall !== 'listen') throw error;

            const bind = typeof port === 'string' ? 'Pipe ' + port : 'Port ' + port;
            switch (error.code) {
                case 'EACCES':
                    console.error(`${bind} requires elevated privileges`);
                    process.exit(1);
                case 'EADDRINUSE':
                    console.error(`${bind} is already in use`);
                    process.exit(1);
                default:
                    throw error;
            }
        }

        function onListening(): void {
            const addr = server.address();
            if (!addr) return;
            const bind =
                typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr.port;
            debug(`Listening on ${bind}`);
            log.info(`server listening on ${bind}`);
        }
    })();

    // Start workers on separate thread
    let worker: Worker | null = null;

    try {
        const workerPath = path.join(wwwDirname, '../workers/main.ts');
        worker = new Worker(workerPath);

        worker.on('message', (msg) => {
            if (msg.type === 'error') {
                log.error('Worker error:', msg.error);
            } else if (msg.type === 'heartbeat') {
                log.debug('Worker heartbeat:', msg.timestamp);
            } else {
                log.info('Worker message:', msg);
            }
        });

        worker.on('error', (error) => {
            log.error('Worker thread error:', error);
        });

        worker.on('exit', (code) => {
            if (code !== 0) {
                log.error(`Worker stopped with exit code ${code}`);
            } else {
                log.info('Worker thread exited gracefully');
            }
        });

        log.info('Worker thread started successfully');
    } catch (error) {
        log.error('Failed to start worker thread:', error);
        // Continue without worker if it fails to start
    }
} else {
    log.info('Running on worker thread');
}

import { getSupabaseClient } from '../lib/middlewares/supabase.authorizer.middleware';
import { logger } from '../lib/logger';
import config from '../lib/config';
import { BulkImportError, BULK_IMPORT_STATUS } from '../lib/constant/queue.constants';

const log = logger(config.logger);

/**
 * Service for sending emails through Supabase
 */
export class SupabaseEmailService {
  /**
   * Send a bulk import completion email
   * @param to Email address of the recipient
   * @param jobId ID of the bulk import job
   * @param fileName Name of the imported file
   * @param status Status of the import job
   * @param stats Import statistics (total, processed, successful, failed)
   * @param errors Array of errors encountered during import
   * @returns Promise resolving to success status
   */
  public static async sendBulkImportCompletionEmail(
    to: string,
    jobId: string,
    fileName: string,
    status: BULK_IMPORT_STATUS,
    stats: {
      totalRows: number;
      processedRows: number;
      successfulRows: number;
      failedRows: number;
    },
    errors: BulkImportError[] = []
  ): Promise<boolean> {
    try {
      log.info(`Sending bulk import completion email to ${to} for job ${jobId}`);

      // Prepare email data
      const subject = `Contact Import ${this.getStatusText(status)}: ${fileName}`;
      
      // Create HTML content with import statistics
      const htmlContent = this.generateBulkImportEmailContent(
        fileName,
        status,
        stats,
        errors
      );

      // Send email using Supabase
      const { error } = await getSupabaseClient().functions.invoke('send-email', {
        body: {
          to,
          subject,
          html: htmlContent
        }
      });

      if (error) {
        log.error(`Error sending bulk import completion email: ${error.message}`);
        return false;
      }

      log.info(`Bulk import completion email sent successfully to ${to} for job ${jobId}`);
      return true;
    } catch (error: unknown) {
      log.error('Error sending bulk import completion email:', error);
      return false;
    }
  }

  /**
   * Send a bulk import error report email
   * @param to Email address of the recipient
   * @param jobId ID of the bulk import job
   * @param fileName Name of the imported file
   * @param errors Array of errors encountered during import
   * @returns Promise resolving to success status
   */
  public static async sendBulkImportErrorReportEmail(
    to: string,
    jobId: string,
    fileName: string,
    errors: BulkImportError[]
  ): Promise<boolean> {
    try {
      log.info(`Sending bulk import error report email to ${to} for job ${jobId}`);

      // Prepare email data
      const subject = `Contact Import Error Report: ${fileName}`;
      
      // Create HTML content with detailed error report
      const htmlContent = this.generateErrorReportEmailContent(
        fileName,
        errors
      );

      // Send email using Supabase
      const { error } = await getSupabaseClient().functions.invoke('send-email', {
        body: {
          to,
          subject,
          html: htmlContent
        }
      });

      if (error) {
        log.error(`Error sending bulk import error report email: ${error.message}`);
        return false;
      }

      log.info(`Bulk import error report email sent successfully to ${to} for job ${jobId}`);
      return true;
    } catch (error: unknown) {
      log.error('Error sending bulk import error report email:', error);
      return false;
    }
  }

  /**
   * Generate HTML content for bulk import completion email
   * @param fileName Name of the imported file
   * @param status Status of the import job
   * @param stats Import statistics
   * @param errors Array of errors encountered during import
   * @returns HTML content for email
   */
  private static generateBulkImportEmailContent(
    fileName: string,
    status: BULK_IMPORT_STATUS,
    stats: {
      totalRows: number;
      processedRows: number;
      successfulRows: number;
      failedRows: number;
    },
    errors: BulkImportError[]
  ): string {
    const statusText = this.getStatusText(status);
    const statusColor = this.getStatusColor(status);
    
    let errorSummary = '';
    if (errors.length > 0) {
      const errorTypes = this.countErrorTypes(errors);
      errorSummary = `
        <h3>Error Summary:</h3>
        <ul>
          ${Object.entries(errorTypes).map(([type, count]) => 
            `<li>${type}: ${count} errors</li>`
          ).join('')}
        </ul>
        <p>Please check the detailed error report for more information.</p>
      `;
    }

    return `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            .status { display: inline-block; padding: 8px 16px; border-radius: 4px; font-weight: bold; color: white; background-color: ${statusColor}; }
            .stats { margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 4px; }
            .stats-item { margin-bottom: 10px; }
            .progress-bar { height: 20px; background-color: #e0e0e0; border-radius: 10px; overflow: hidden; }
            .progress-fill { height: 100%; background-color: #4caf50; }
            .footer { margin-top: 30px; font-size: 12px; color: #777; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Contact Import ${statusText}</h2>
              <div class="status">${statusText}</div>
            </div>
            
            <p>Your contact import for file <strong>${fileName}</strong> has ${status === BULK_IMPORT_STATUS.COMPLETED ? 'completed' : 'finished'} with the following results:</p>
            
            <div class="stats">
              <div class="stats-item">
                <strong>Total Rows:</strong> ${stats.totalRows}
              </div>
              <div class="stats-item">
                <strong>Processed Rows:</strong> ${stats.processedRows} (${Math.round((stats.processedRows / stats.totalRows) * 100)}%)
                <div class="progress-bar">
                  <div class="progress-fill" style="width: ${Math.round((stats.processedRows / stats.totalRows) * 100)}%;"></div>
                </div>
              </div>
              <div class="stats-item">
                <strong>Successfully Imported:</strong> ${stats.successfulRows} (${Math.round((stats.successfulRows / stats.totalRows) * 100)}%)
                <div class="progress-bar">
                  <div class="progress-fill" style="width: ${Math.round((stats.successfulRows / stats.totalRows) * 100)}%;"></div>
                </div>
              </div>
              <div class="stats-item">
                <strong>Failed Rows:</strong> ${stats.failedRows} (${Math.round((stats.failedRows / stats.totalRows) * 100)}%)
              </div>
            </div>
            
            ${errorSummary}
            
            <p>Thank you for using ShoutOUT Engage!</p>
            
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Generate HTML content for error report email
   * @param fileName Name of the imported file
   * @param errors Array of errors encountered during import
   * @returns HTML content for email
   */
  private static generateErrorReportEmailContent(
    fileName: string,
    errors: BulkImportError[]
  ): string {
    // Group errors by type
    const errorsByType: Record<string, BulkImportError[]> = {};
    errors.forEach(error => {
      if (!errorsByType[error.type]) {
        errorsByType[error.type] = [];
      }
      errorsByType[error.type].push(error);
    });

    // Generate error tables by type
    const errorTables = Object.entries(errorsByType).map(([type, typeErrors]) => {
      return `
        <h3>${this.formatErrorType(type)} Errors (${typeErrors.length})</h3>
        <table border="1" cellpadding="5" cellspacing="0" width="100%">
          <thead>
            <tr style="background-color: #f2f2f2;">
              <th>Row</th>
              <th>Field</th>
              <th>Value</th>
              <th>Message</th>
            </tr>
          </thead>
          <tbody>
            ${typeErrors.map(error => `
              <tr>
                <td>${error.row}</td>
                <td>${error.field || 'N/A'}</td>
                <td>${error.value || 'N/A'}</td>
                <td>${error.message}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      `;
    }).join('<br>');

    return `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 800px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
            th { background-color: #f2f2f2; text-align: left; }
            td, th { padding: 8px; border: 1px solid #ddd; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            .footer { margin-top: 30px; font-size: 12px; color: #777; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Contact Import Error Report</h2>
            </div>
            
            <p>The following errors were encountered while importing contacts from <strong>${fileName}</strong>:</p>
            
            ${errorTables}
            
            <p>Please correct these errors and try importing again.</p>
            
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Get human-readable status text
   * @param status Status enum value
   * @returns Human-readable status text
   */
  private static getStatusText(status: BULK_IMPORT_STATUS): string {
    switch (status) {
      case BULK_IMPORT_STATUS.COMPLETED:
        return 'Completed';
      case BULK_IMPORT_STATUS.FAILED:
        return 'Failed';
      case BULK_IMPORT_STATUS.PROCESSING:
        return 'In Progress';
      case BULK_IMPORT_STATUS.PENDING:
        return 'Pending';
      default:
        return 'Unknown';
    }
  }

  /**
   * Get color for status
   * @param status Status enum value
   * @returns CSS color for status
   */
  private static getStatusColor(status: BULK_IMPORT_STATUS): string {
    switch (status) {
      case BULK_IMPORT_STATUS.COMPLETED:
        return '#4caf50'; // Green
      case BULK_IMPORT_STATUS.FAILED:
        return '#f44336'; // Red
      case BULK_IMPORT_STATUS.PROCESSING:
        return '#2196f3'; // Blue
      case BULK_IMPORT_STATUS.PENDING:
        return '#ff9800'; // Orange
      default:
        return '#9e9e9e'; // Grey
    }
  }

  /**
   * Format error type for display
   * @param errorType Error type from enum
   * @returns Formatted error type
   */
  private static formatErrorType(errorType: string): string {
    return errorType.charAt(0).toUpperCase() + errorType.slice(1).toLowerCase();
  }

  /**
   * Count errors by type
   * @param errors Array of errors
   * @returns Object with error counts by type
   */
  private static countErrorTypes(errors: BulkImportError[]): Record<string, number> {
    const counts: Record<string, number> = {};
    errors.forEach(error => {
      if (!counts[error.type]) {
        counts[error.type] = 0;
      }
      counts[error.type]++;
    });
    return counts;
  }
}
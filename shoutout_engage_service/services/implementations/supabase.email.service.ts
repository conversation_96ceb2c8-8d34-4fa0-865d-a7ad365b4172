import { getSupabaseClient } from '../../lib/middlewares/supabase.authorizer.middleware';
import { logger } from '../../lib/logger';
import config from '../../lib/config';
import { BULK_IMPORT_STATUS } from '../../lib/constant/queue.constants';
import { 
  EmailData, 
  IEmailService, 
  EmailTemplateData,
  BulkImportCompletionTemplateData,
  BulkImportErrorReportTemplateData
} from '../../types/email.types';
import { EmailTemplate, TemplateLoader } from '../../lib/utils/template.loader';

const log = logger(config.logger);

/**
 * Supabase implementation of the email service
 */
export class SupabaseEmailService implements IEmailService {
  /**
   * Send an email
   * @param emailData Email data
   * @returns Promise resolving to success status
   */
  public async send(emailData: EmailData): Promise<boolean> {
    try {
      log.info(`Sending email to ${emailData.to}`);

      // Send email using Supabase
      const { error } = await getSupabaseClient().functions.invoke('send-email', {
        body: emailData
      });

      if (error) {
        log.error(`Error sending email: ${error.message}`);
        return false;
      }

      log.info(`Email sent successfully to ${emailData.to}`);
      return true;
    } catch (error: unknown) {
      log.error('Error sending email:', error);
      return false;
    }
  }

  /**
   * Send an email using a template
   * @param to Recipient email address
   * @param template Email template to use
   * @param templateData Data to populate the template
   * @returns Promise resolving to success status
   */
  public async sendTemplate(
    to: string,
    template: EmailTemplate,
    templateData: EmailTemplateData
  ): Promise<boolean> {
    try {
      log.info(`Sending template email ${template} to ${to}`);

      // Prepare email data based on template type
      let subject: string;
      let variables: Record<string, any>;

      switch (template) {
        case EmailTemplate.BULK_IMPORT_COMPLETION:
          const completionData = templateData as BulkImportCompletionTemplateData;
          subject = `Contact Import ${this.getStatusText(completionData.status)}: ${completionData.fileName}`;
          variables = this.prepareBulkImportCompletionVariables(completionData);
          break;

        case EmailTemplate.BULK_IMPORT_ERROR_REPORT:
          const errorReportData = templateData as BulkImportErrorReportTemplateData;
          subject = `Contact Import Error Report: ${errorReportData.fileName}`;
          variables = this.prepareBulkImportErrorReportVariables(errorReportData);
          break;

        default:
          log.error(`Unknown template: ${template}`);
          return false;
      }

      // Compile the template with variables
      const html = TemplateLoader.compile(template, variables);

      // Send the email
      return await this.send({
        to,
        subject,
        html
      });
    } catch (error: unknown) {
      log.error(`Error sending template email ${template}:`, error);
      return false;
    }
  }

  /**
   * Prepare variables for bulk import completion template
   * @param data Bulk import completion template data
   * @returns Variables for template
   */
  private prepareBulkImportCompletionVariables(data: BulkImportCompletionTemplateData): Record<string, any> {
    const { status, fileName, stats, errors } = data;
    
    // Calculate percentages
    const processedPercentage = stats.totalRows > 0 
      ? Math.round((stats.processedRows / stats.totalRows) * 100) 
      : 0;
    
    const successPercentage = stats.totalRows > 0 
      ? Math.round((stats.successfulRows / stats.totalRows) * 100) 
      : 0;
    
    const failedPercentage = stats.totalRows > 0 
      ? Math.round((stats.failedRows / stats.totalRows) * 100) 
      : 0;

    // Generate error summary if there are errors
    const errorSummary = errors && errors.length > 0 
      ? TemplateLoader.generateErrorSummary(errors) 
      : '';

    return {
      statusText: this.getStatusText(status),
      statusColor: this.getStatusColor(status),
      fileName,
      completionText: status === BULK_IMPORT_STATUS.COMPLETED ? 'completed' : 'finished',
      totalRows: stats.totalRows,
      processedRows: stats.processedRows,
      successfulRows: stats.successfulRows,
      failedRows: stats.failedRows,
      processedPercentage,
      successPercentage,
      failedPercentage,
      errorSummary
    };
  }

  /**
   * Prepare variables for bulk import error report template
   * @param data Bulk import error report template data
   * @returns Variables for template
   */
  private prepareBulkImportErrorReportVariables(data: BulkImportErrorReportTemplateData): Record<string, any> {
    const { fileName, errors } = data;

    return {
      fileName,
      errorTables: TemplateLoader.generateErrorTables(errors)
    };
  }

  /**
   * Get human-readable status text
   * @param status Status enum value
   * @returns Human-readable status text
   */
  private getStatusText(status: BULK_IMPORT_STATUS): string {
    switch (status) {
      case BULK_IMPORT_STATUS.COMPLETED:
        return 'Completed';
      case BULK_IMPORT_STATUS.FAILED:
        return 'Failed';
      case BULK_IMPORT_STATUS.PROCESSING:
        return 'In Progress';
      case BULK_IMPORT_STATUS.PENDING:
        return 'Pending';
      default:
        return 'Unknown';
    }
  }

  /**
   * Get color for status
   * @param status Status enum value
   * @returns CSS color for status
   */
  private getStatusColor(status: BULK_IMPORT_STATUS): string {
    switch (status) {
      case BULK_IMPORT_STATUS.COMPLETED:
        return '#4caf50'; // Green
      case BULK_IMPORT_STATUS.FAILED:
        return '#f44336'; // Red
      case BULK_IMPORT_STATUS.PROCESSING:
        return '#2196f3'; // Blue
      case BULK_IMPORT_STATUS.PENDING:
        return '#ff9800'; // Orange
      default:
        return '#9e9e9e'; // Grey
    }
  }
}
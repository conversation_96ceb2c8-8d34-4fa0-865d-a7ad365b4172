# An .aiignore file follows the same syntax as a .gitignore file.
# .gitignore documentation: https://git-scm.com/docs/gitignore
# <PERSON><PERSON> will ask for explicit approval before view or edit the file or file within a directory listed in .aiignore.
# Only files contents is protected, <PERSON><PERSON> is still allowed to view file names even if they are listed in .aiignore.
# Be aware that the files you included in .aiignore can still be accessed by <PERSON><PERSON> in two cases:
# - If Brave Mode is turned on.
# - If a command has been added to the Allowlist — <PERSON><PERSON> will not ask for confirmation, even if it accesses - files and folders listed in .aiignore.

# Ignore all .env files to protect sensitive environment variables
*.env*

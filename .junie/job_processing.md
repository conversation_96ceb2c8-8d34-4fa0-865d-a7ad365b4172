# Job Processing with Redis and <PERSON> in TypeScript

This document explains how job processing is implemented in our system using Redis and Bull MQ with TypeScript. It covers the configuration, architecture, and provides a simple example to help replicate this functionality in another system.

## Table of Contents

1. [Overview](#overview)
2. [Redis Configuration](#redis-configuration)
3. [Bull MQ Implementation](#bull-mq-implementation)
4. [Job Queue Architecture](#job-queue-architecture)
5. [Example: Transaction Import Job Processing](#example-transaction-import-job-processing)
6. [Implementing in Another System](#implementing-in-another-system)

## Overview

Our system uses Redis as a message broker and Bull MQ as a job queue library to handle asynchronous processing of various tasks. This architecture allows us to:

- Process jobs in the background
- Handle high-volume workloads
- Ensure job persistence and reliability
- Implement job retry mechanisms
- Monitor job progress and status

## Redis Configuration

Redis serves as the backend for Bull MQ, storing job data, states, and other metadata.

### Redis Connection Setup

Redis connection is configured using environment variables with proper TypeScript typing:

```typescript
// RedisConnector.ts
import * as redis from 'redis';
import { promisify } from 'util';

// Define interface for Redis client options
interface RedisClientOptions {
    host: string;
    port: number;
}

// Create typed Redis client options
const redisClientOptions: RedisClientOptions = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10)
};

// Create Redis client with typed options
const client = redis.createClient(redisClientOptions);
if (process.env.REDIS_PASSWORD) {
    client.auth(process.env.REDIS_PASSWORD);
}
```

The `RedisConnector` class provides methods to:
- Get Redis configuration for Bull MQ
- Access promisified Redis commands
- Manage Redis connections
- Perform utility operations like pattern-based key deletion

```typescript
// RedisConnector.ts
// Define interface for Redis configuration
interface RedisConfig {
    host: string;
    port: number;
    password?: string;
}

export class RedisConnector {
    // Return typed Redis configuration
    static getConfig(): RedisConfig {
        return process.env.REDIS_PASSWORD
            ? Object.assign({}, redisClientOptions, { password: process.env.REDIS_PASSWORD })
            : redisClientOptions;
    }
    
    // Example of promisified Redis commands with proper typing
    static getCommands() {
        const getAsync = promisify(client.get).bind(client);
        const setAsync = promisify(client.set).bind(client);
        
        return {
            getAsync,
            setAsync
            // Other commands...
        };
    }
}
```

## Bull MQ Implementation

Bull MQ is used to create and manage job queues. Each type of job has its own queue.

### Queue Creation

Queues are created with a name and Redis configuration, with proper TypeScript interfaces:

```typescript
// TransactionsImportJobProcessor.ts
import Queue from 'bull';
import { RedisConnector } from '../lib/db/connectors/RedisConnector';
import { QUEUES } from '../constants';

// Define interface for queue options
interface QueueOptions {
    redis: {
        host: string;
        port: number;
        password?: string;
    };
    defaultJobOptions: {
        attempts: number;
        timeout: number;
        removeOnComplete: { count: number } | boolean;
        removeOnFail: { count: number } | boolean;
    };
}

// Create queue with typed options
const queue = new Queue(QUEUES.TRANSACTION_IMPORT_JOBS_QUEUE, {
    redis: RedisConnector.getConfig(),
    defaultJobOptions: {
        attempts: 1,
        timeout: 24 * 60 * 60 * 1000, // 24 hours
        removeOnComplete: { count: 1000 },
        removeOnFail: { count: 1000 }
    }
});
```

### Job Processing

Job processors define how jobs are handled, with proper TypeScript typing for job data and error handling:

```typescript
// TransactionsImportJobProcessor.ts
import { Job } from 'bull';

export class TransactionsImportJobProcessor {
    static startProcess(): void {
        queue.process(async function (job: Job): Promise<void> {
            try {
                // Process job data
                // ...
                return Promise.resolve();
            } catch (err: unknown) {
                // TypeScript unknown type for better error handling
                return Promise.reject(err);
            }
        });
    }
}
```

### Event Handling

Bull MQ provides events for monitoring job lifecycle, with proper TypeScript typing:

```typescript
// TransactionsImportJobProcessor.ts
import { Job } from 'bull';
import { Logger } from '../lib/logger';
import { config } from '../lib/config';

// Type the logger
const log: Logger = logger(config.logger);

// Type the job parameter
queue.on('completed', function (job: Job): void {
    log.info(`job ${job.id} completed`);
});

// Other events: 'failed', 'progress', 'stalled', etc.
```

## Job Queue Architecture

Our system uses multiple specialized queues for different types of jobs, with TypeScript exports:

```typescript
// constants.ts
// Export constants for use in other files
export const QUEUES = {
    TRANSACTION_IMPORT_JOBS_QUEUE: 'transaction_import_jobs',
    TRANSACTIONS_QUEUE: 'transaction_jobs',
    REWARDS_TOPUP_JOB_QUEUE: 'reward_topup_jobs',
    // ... many more queues
};

export const DEFAULT_OPTIONS = {
    attempts: 1,
    timeout: 24 * 60 * 60 * 1000,
    removeOnComplete: { count: 1000 },
    removeOnFail: { count: 1000 }
};

export const CATEGORY = { 
    SYSTEM: 'SYSTEM', 
    CORE: 'CORE', 
    CAMPAIGN: 'CAMPAIGN', 
    UNLISTED: 'UNLISTED' 
};
```

### Worker Initialization

Workers are initialized in the main worker file, with proper TypeScript async function and imports:

```typescript
// main.ts
import { MongooseConnector } from '../lib/db/connectors/MongooseConnector';
import { TransactionsImportJobProcessor } from './processors/transactions.import.job.processor';
import { TransactionsProcessor } from './processors/transactions.processor';
import { config } from '../lib/config';
import { logger, Logger } from '../lib/logger';

// Type the logger
const log: Logger = logger(config.logger);

// Use TypeScript async arrow function with return type
(async (): Promise<void> => {
    await MongooseConnector.initialize();
    log.info('starting worker processes...');
    TransactionsImportJobProcessor.startProcess();
    TransactionsProcessor.startProcess();
    // ... initialize other processors
})();
```

## Example: Transaction Import Job Processing

Let's walk through a complete example of job processing using the `TransactionsImportJobProcessor` with TypeScript.

### 1. Queue Creation

```typescript
// transactions.import.job.processor.ts
import Queue from 'bull';
import { RedisConnector } from '../../lib/db/connectors/RedisConnector';
import { QUEUES } from '../constants';

const queue = new Queue(QUEUES.TRANSACTION_IMPORT_JOBS_QUEUE, {
    redis: RedisConnector.getConfig(),
    defaultJobOptions: {
        attempts: 1,
        timeout: 24 * 60 * 60 * 1000,
        removeOnComplete: { count: 1000 },
        removeOnFail: { count: 1000 }
    }
});
```

### 2. Job Processing

```typescript
// transactions.import.job.processor.ts
import { Job } from 'bull';
import { Logger } from '../../lib/logger';
import { TransactionImportJobsDAO } from '../../lib/db/dao/TransactionImportJobsDAO';
import { StorageWrapper } from '../../lib/wrappers/StorageWrapper';
import { STATUS, SYSTEM_ATTRIBUTE_NAME, MERCHANT_LOCATION_SELECTION } from '../../lib/db/models/enums/transaction.import.job.enums';
import * as mongoose from 'mongoose';
import * as csv from 'csv-parser';
import { find, has, trim } from 'lodash';
import { config } from '../../lib/config';
import { logger } from '../../lib/logger';
import { TransactionsProcessor } from './transactions.processor';

const log = logger(config.logger);
const transactionsQueue = TransactionsProcessor.getQueue();
const USER_ASSETS_BUCKET = 'user-assets-bucket'; // This would come from config

// Define TypeScript interfaces for data structures
interface FieldMapping {
    fileColumnName: string;
    systemAttributeName: string;
}

interface TransactionData {
    merchantId: string;
    cardNo: string;
    transactionDate: string;
    transactionType: string;
    transactionSubTypeId: string;
    invoiceData: {
        invoiceId?: string;
    };
    merchantLocationId?: string;
    merchantLocationCode?: string;
    billAmount?: string;
    pointsAmount?: string;
    stagedTransaction?: boolean;
}

interface TransactionProcessJob {
    organizationId: string;
    regionId: string;
    transactionsImportJobId: string;
    transaction: TransactionData;
    callerId: string;
    rawData: Record<string, any>;
    evaluatePointRules: boolean;
}

// Helper functions with TypeScript typing
const mapHeader = (header: string, fieldMappings: FieldMapping[]): string => {
    const fieldMapping = find(fieldMappings, (mapping) => {
        return mapping.fileColumnName === header;
    });
    return fieldMapping ? fieldMapping.systemAttributeName : header.toLowerCase();
};

const mapValue = (header: string, value: string): string => {
    return trim(value);
};

export class TransactionsImportJobProcessor {
    static startProcess(): void {
        queue.process(async function (job: Job): Promise<void> {
            try {
                log.info(`starting transactions import job ${job.id}`);
                
                // Extract job data with TypeScript destructuring
                const {
                    organizationId,
                    regionId,
                    merchantId,
                    merchantLocationSelection,
                    merchantLocationId,
                    merchantLocationColumnName,
                    transactionType,
                    transactionSubTypeId,
                    fileId,
                    fieldMappings,
                    callerId,
                    allowedMerchantLocationIds,
                    allowedMerchantLocationCodes,
                    locationCodeMap
                } = job.data;
                
                const jobId = job.id;
                
                // Update job status
                const initialJobUpdate = {
                    status: STATUS.PROCESSING,
                    startedOn: new Date()
                };
                
                const updatedJob = await TransactionImportJobsDAO.updateTransactionImportJob(
                    initialJobUpdate,
                    jobId,
                    organizationId,
                    { status: STATUS.PENDING }
                );
                
                if (!updatedJob) {
                    return Promise.reject(new Error(`transaction import job status is not ${STATUS.PENDING}`));
                }
                
                // Process CSV file
                let totalRecordsCount = 0;
                const stream = await StorageWrapper.getFileStream(USER_ASSETS_BUCKET, fileId);
                stream
                    .pipe(csv({
                        mapHeaders: ({ header }: { header: string }): string => mapHeader(header, fieldMappings),
                        mapValues: ({ header, value }: { header: string, value: string }): string => mapValue(header, value)
                    }))
                    .on('data', async (data: Record<string, any>): Promise<void> => {
                        totalRecordsCount++;
                        
                        // Create typed object
                        const invoiceData: { invoiceId?: string } = {};
                        if (has(data, SYSTEM_ATTRIBUTE_NAME.BILL_REFERENCE)) {
                            invoiceData.invoiceId = data[SYSTEM_ATTRIBUTE_NAME.BILL_REFERENCE];
                        }
                        
                        // Create transaction object with proper typing
                        const transaction: TransactionData = {
                            merchantId,
                            cardNo: data[SYSTEM_ATTRIBUTE_NAME.LOYALTY_CARD],
                            transactionDate: data[SYSTEM_ATTRIBUTE_NAME.TRANSACTION_DATE],
                            transactionType,
                            transactionSubTypeId,
                            invoiceData
                        };
                        
                        if (merchantLocationSelection === MERCHANT_LOCATION_SELECTION.STATIC) {
                            transaction.merchantLocationId = merchantLocationId;
                            if (!allowedMerchantLocationIds.includes(merchantLocationId))
                                transaction.stagedTransaction = true;
                        } else if (merchantLocationSelection === MERCHANT_LOCATION_SELECTION.DYNAMIC) {
                            transaction.merchantLocationCode = data[merchantLocationColumnName];
                            if (locationCodeMap[data[merchantLocationColumnName]])
                                transaction.merchantLocationId = locationCodeMap[data[merchantLocationColumnName]];
                            if (!allowedMerchantLocationCodes.includes(data[merchantLocationColumnName]))
                                transaction.stagedTransaction = true;
                        }
                        
                        let evaluatePointRules = false;
                        
                        if (has(data, SYSTEM_ATTRIBUTE_NAME.BILL_AMOUNT)) {
                            transaction.billAmount = data[SYSTEM_ATTRIBUTE_NAME.BILL_AMOUNT];
                            if (Number(transaction.billAmount) < 0) evaluatePointRules = true;
                        } else if (has(data, SYSTEM_ATTRIBUTE_NAME.POINTS_AMOUNT)) {
                            transaction.pointsAmount = data[SYSTEM_ATTRIBUTE_NAME.POINTS_AMOUNT];
                        }
                        
                        // Create job for transaction processing with proper typing
                        const transactionProcessJob: TransactionProcessJob = {
                            organizationId,
                            regionId,
                            transactionsImportJobId: jobId,
                            transaction,
                            callerId,
                            rawData: data,
                            evaluatePointRules
                        };
                        
                        // Add job to transactions queue
                        await transactionsQueue.add(transactionProcessJob, {
                            jobId: mongoose.Types.ObjectId().toString()
                        });
                    })
                    .on('end', async (): Promise<void> => {
                        // Update job completion
                        const completionJobUpdate = {
                            totalRecordsCount
                        };
                        
                        await TransactionImportJobsDAO.updateTransactionImportJob(
                            completionJobUpdate,
                            jobId,
                            organizationId
                        );
                        job.progress(100);
                        return Promise.resolve();
                    });
            } catch (err: unknown) {
                // TypeScript unknown type for better error handling
                log.error(err);
                return Promise.reject(err);
            }
        });
    }
    
    // Return typed queue
    static getQueue(): Queue.Queue {
        return queue;
    }
}
```

### 3. Job Creation (from another part of the application)

```typescript
// Example of how a job might be created
import { TransactionsImportJobProcessor } from './processors/transactions.import.job.processor';
import * as mongoose from 'mongoose';

// Define TypeScript interfaces
interface FieldMapping {
    fileColumnName: string;
    systemAttributeName: string;
}

interface JobData {
    organizationId: string;
    regionId: string;
    merchantId: string;
    fileId: string;
    fieldMappings: FieldMapping[];
    [key: string]: any; // Index signature for additional properties
}

async function createTransactionImportJob(): Promise<void> {
    const transactionsImportQueue = TransactionsImportJobProcessor.getQueue();
    
    // Create typed job data
    const jobData: JobData = {
        organizationId: 'org123',
        regionId: 'region456',
        merchantId: 'merchant789',
        fileId: 'file123',
        fieldMappings: [
            { fileColumnName: 'Card Number', systemAttributeName: 'loyaltyCard' },
            { fileColumnName: 'Transaction Date', systemAttributeName: 'transactionDate' },
            // ... other field mappings
        ],
        // ... other job data
    };

    const jobId = mongoose.Types.ObjectId().toString();
    await transactionsImportQueue.add(jobData, { jobId });
}
```

## Implementing in Another System

To implement this job processing architecture in another system using TypeScript:

1. **Set up Redis**:
    - Install Redis server
    - Configure environment variables: `REDIS_HOST`, `REDIS_PORT`, `REDIS_PASSWORD`

2. **Install Dependencies**:
   ```bash
   npm install bull redis @types/bull @types/redis typescript
   ```

3. **Create Redis Connector**:
   ```typescript
   // RedisConnector.ts
   import * as redis from 'redis';
   
   // Define TypeScript interface
   interface RedisConfig {
       host: string;
       port: number;
       password?: string;
   }
   
   export class RedisConnector {
       // Return typed configuration
       static getConfig(): RedisConfig {
           return {
               host: process.env.REDIS_HOST || 'localhost',
               port: parseInt(process.env.REDIS_PORT || '6379', 10),
               password: process.env.REDIS_PASSWORD
           };
       }
   }
   ```

4. **Define Queue Constants**:
   ```typescript
   // constants.ts
   // Export constants for use in other files
   export const QUEUES = {
       MY_JOB_QUEUE: 'my_job_queue'
   };
   
   export const DEFAULT_OPTIONS = {
       attempts: 3,
       timeout: 60 * 1000, // 1 minute
       removeOnComplete: true,
       removeOnFail: false
   };
   ```

5. **Create Job Processor**:
   ```typescript
   // MyJobProcessor.ts
   import Queue, { Job } from 'bull';
   import { RedisConnector } from './RedisConnector';
   import { QUEUES } from './constants';
   
   // Define TypeScript interface for job data
   interface JobData {
       data1: string;
       data2: number;
       [key: string]: any; // Index signature for additional properties
   }
   
   const queue = new Queue(QUEUES.MY_JOB_QUEUE, {
       redis: RedisConnector.getConfig(),
       defaultJobOptions: {
           attempts: 3,
           timeout: 60 * 1000,
           removeOnComplete: true,
           removeOnFail: false
       }
   });
   
   // Define typed async function
   async function someAsyncOperation(data1: string, data2: number): Promise<any> {
       // Implementation of the operation
       return { result: `Processed ${data1} and ${data2}` };
   }
   
   export class MyJobProcessor {
       static startProcess(): void {
           // Use generic Job type with JobData interface
           queue.process(async function(job: Job<JobData>): Promise<any> {
               try {
                   console.log(`Processing job ${job.id}`);
                   const { data1, data2 } = job.data;
                   
                   // Process job data
                   const result = await someAsyncOperation(data1, data2);
                   
                   // Update job progress
                   job.progress(100);
                   
                   return result;
               } catch (err: unknown) {
                   // TypeScript unknown type for better error handling
                   console.error(`Job ${job.id} failed:`, err);
                   throw err;
               }
           });
       }
       
       // Return typed queue
       static getQueue(): Queue.Queue {
           return queue;
       }
   }
   ```

6. **Initialize Worker**:
   ```typescript
   // worker.ts
   import { MyJobProcessor } from './MyJobProcessor';
   
   // Use TypeScript async arrow function with return type
   (async (): Promise<void> => {
       console.log('Starting worker processes...');
       MyJobProcessor.startProcess();
   })();
   ```

7. **Create Jobs**:
   ```typescript
   // somewhere in your application
   import { MyJobProcessor } from './MyJobProcessor';
   import { v4 as uuidv4 } from 'uuid';
   
   // Define TypeScript interface for job data
   interface JobData {
       data1: string;
       data2: number;
       [key: string]: any; // Index signature for additional properties
   }
   
   // Define typed async function
   async function createJob(data: JobData): Promise<void> {
       const queue = MyJobProcessor.getQueue();
       await queue.add(data, {
           jobId: generateUniqueId(),
           priority: 1
       });
   }
   
   // Define typed function
   function generateUniqueId(): string {
       return uuidv4();
   }
   ```

By following these steps, you can implement a similar job processing architecture in another system using Redis and Bull MQ with TypeScript, taking advantage of TypeScript's type safety and other features.